<template>
  <div class="qps-chart">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as d3 from 'd3'

interface ChartData {
  time: string
  value: number
}

interface Props {
  data: ChartData[]
}

const props = defineProps<Props>()

const chartContainer = ref<HTMLElement>()
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
let xScale: d3.ScalePoint<string>
let yScale: d3.ScaleLinear<number, number>
let area: d3.Area<ChartData>

const margin = { top: 20, right: 30, bottom: 40, left: 50 }
const width = 400 - margin.left - margin.right
const height = 150 - margin.bottom - margin.top

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (svg) {
    svg.remove()
  }
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartContainer.value) return

  // 创建SVG
  svg = d3.select(chartContainer.value)
    .append('svg')
    .attr('width', width + margin.left + margin.right)
    .attr('height', height + margin.top + margin.bottom)

  const g = svg.append('g')
    .attr('transform', `translate(${margin.left},${margin.top})`)

  // 创建比例尺
  xScale = d3.scalePoint()
    .range([0, width])
    .padding(0.1)

  yScale = d3.scaleLinear()
    .range([height, 0])

  // 创建面积生成器
  area = d3.area<ChartData>()
    .x(d => xScale(d.time) || 0)
    .y0(height)
    .y1(d => yScale(d.value))
    .curve(d3.curveMonotoneX)

  // 添加渐变定义
  const defs = svg.append('defs')
  const gradient = defs.append('linearGradient')
    .attr('id', 'qps-gradient')
    .attr('gradientUnits', 'userSpaceOnUse')
    .attr('x1', 0).attr('y1', 0)
    .attr('x2', 0).attr('y2', height)

  gradient.append('stop')
    .attr('offset', '0%')
    .attr('stop-color', '#722ed1')
    .attr('stop-opacity', 0.8)

  gradient.append('stop')
    .attr('offset', '100%')
    .attr('stop-color', '#722ed1')
    .attr('stop-opacity', 0.1)

  // 添加X轴
  g.append('g')
    .attr('class', 'x-axis')
    .attr('transform', `translate(0,${height})`)

  // 添加Y轴
  g.append('g')
    .attr('class', 'y-axis')

  // 添加Y轴标签
  g.append('text')
    .attr('class', 'y-label')
    .attr('transform', 'rotate(-90)')
    .attr('y', 0 - margin.left)
    .attr('x', 0 - (height / 2))
    .attr('dy', '1em')
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#666')
    .text('QPS (req/s)')

  // 添加面积路径
  g.append('path')
    .attr('class', 'area')
    .style('fill', 'url(#qps-gradient)')

  // 添加线条路径
  g.append('path')
    .attr('class', 'line')
    .style('fill', 'none')
    .style('stroke', '#722ed1')
    .style('stroke-width', 2)

  updateChart()
}

const updateChart = () => {
  if (!svg || !props.data.length) return

  const g = svg.select('g')

  // 更新比例尺域
  xScale.domain(props.data.map(d => d.time))
  yScale.domain([0, d3.max(props.data, d => d.value) || 10])

  // 创建线条生成器
  const line = d3.line<ChartData>()
    .x(d => xScale(d.time) || 0)
    .y(d => yScale(d.value))
    .curve(d3.curveMonotoneX)

  // 更新X轴
  g.select('.x-axis')
    .transition()
    .duration(300)
    .call(d3.axisBottom(xScale)
      .tickFormat((d, i) => i % 5 === 0 ? d : '') // 只显示每5个刻度
    )
    .selectAll('text')
    .style('font-size', '10px')
    .style('fill', '#666')

  // 更新Y轴
  g.select('.y-axis')
    .transition()
    .duration(300)
    .call(d3.axisLeft(yScale).ticks(5))
    .selectAll('text')
    .style('font-size', '10px')
    .style('fill', '#666')

  // 更新面积
  g.select('.area')
    .datum(props.data)
    .transition()
    .duration(300)
    .attr('d', area)

  // 更新线条
  g.select('.line')
    .datum(props.data)
    .transition()
    .duration(300)
    .attr('d', line)

  // 添加数据点（用于悬停效果）
  const dots = g.selectAll('.qps-dot')
    .data(props.data)

  dots.enter()
    .append('circle')
    .attr('class', 'qps-dot')
    .attr('r', 0)
    .style('fill', '#722ed1')
    .style('opacity', 0)
    .merge(dots)
    .transition()
    .duration(300)
    .attr('cx', d => xScale(d.time) || 0)
    .attr('cy', d => yScale(d.value))

  dots.exit().remove()

  // 添加悬停效果
  g.selectAll('.qps-dot')
    .on('mouseover', function(event, d) {
      d3.select(this)
        .transition()
        .duration(100)
        .attr('r', 4)
        .style('opacity', 1)

      // 显示提示框
      const tooltip = d3.select('body')
        .append('div')
        .attr('class', 'chart-tooltip')
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', 'white')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000')
        .html(`时间: ${d.time}<br/>QPS: ${d.value.toFixed(1)} req/s`)

      const [x, y] = d3.pointer(event, document.body)
      tooltip
        .style('left', (x + 10) + 'px')
        .style('top', (y - 10) + 'px')
    })
    .on('mouseout', function() {
      d3.select(this)
        .transition()
        .duration(100)
        .attr('r', 0)
        .style('opacity', 0)

      d3.selectAll('.chart-tooltip').remove()
    })

  // 添加鼠标移动效果
  g.append('rect')
    .attr('class', 'overlay')
    .attr('width', width)
    .attr('height', height)
    .style('fill', 'none')
    .style('pointer-events', 'all')
    .on('mousemove', function(event) {
      const [mouseX] = d3.pointer(event, this)
      const bisect = d3.bisector((d: ChartData) => xScale(d.time) || 0).left
      const index = bisect(props.data, mouseX)
      
      if (index > 0 && index < props.data.length) {
        const d = props.data[index]
        
        // 高亮最近的数据点
        g.selectAll('.qps-dot')
          .style('opacity', (_, i) => i === index ? 1 : 0)
          .attr('r', (_, i) => i === index ? 4 : 0)
      }
    })
    .on('mouseleave', function() {
      g.selectAll('.qps-dot')
        .style('opacity', 0)
        .attr('r', 0)
    })
}
</script>

<style scoped>
.qps-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}

:deep(.x-axis) .domain,
:deep(.y-axis) .domain {
  stroke: #d9d9d9;
}

:deep(.x-axis) .tick line,
:deep(.y-axis) .tick line {
  stroke: #d9d9d9;
}
</style>
