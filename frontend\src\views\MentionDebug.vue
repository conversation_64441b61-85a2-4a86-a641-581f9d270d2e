<template>
  <div class="mention-debug">
    <a-page-header
      title="@导演功能调试"
      sub-title="调试和测试@mention功能的详细信息"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag :color="testStatus === 'success' ? 'green' : testStatus === 'error' ? 'red' : 'blue'">
            状态: {{ testStatus }}
          </a-tag>
          <a-button @click="runDebugTest" :loading="testing">
            <template #icon><BugOutlined /></template>
            运行调试
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="debug-content">
      <a-row :gutter="16">
        <!-- 左侧：测试控制 -->
        <a-col :span="12">
          <a-card title="调试测试" size="small">
            <a-form layout="vertical">
              <a-form-item label="测试消息">
                <a-input
                  v-model:value="testMessage"
                  placeholder="输入包含@导演的测试消息"
                />
              </a-form-item>
              
              <a-form-item>
                <a-space direction="vertical" style="width: 100%">
                  <a-button 
                    type="primary" 
                    block
                    @click="testMentionParsing"
                    :disabled="!testMessage.trim()"
                  >
                    测试@mention解析
                  </a-button>
                  
                  <a-button 
                    block
                    @click="testHttpApi"
                    :disabled="!testMessage.trim()"
                    :loading="testingHttp"
                  >
                    测试HTTP API
                  </a-button>
                  
                  <a-button 
                    block
                    @click="testChatStore"
                    :disabled="!testMessage.trim()"
                    :loading="testingStore"
                  >
                    测试ChatStore
                  </a-button>
                  
                  <a-button 
                    block
                    @click="clearDebugLog"
                  >
                    清空日志
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>

            <a-divider>快速测试</a-divider>
            
            <a-space direction="vertical" style="width: 100%">
              <a-button block @click="quickTest('@导演 请帮我规划一个科幻故事')">
                测试1: @导演 科幻故事
              </a-button>
              <a-button block @click="quickTest('@设计师 设计一个机器人角色')">
                测试2: @设计师 机器人角色
              </a-button>
              <a-button block @click="quickTest('@撰写师 写一段开头')">
                测试3: @撰写师 故事开头
              </a-button>
            </a-space>
          </a-card>
        </a-col>

        <!-- 右侧：调试日志 -->
        <a-col :span="12">
          <a-card title="调试日志" size="small">
            <template #extra>
              <a-tag color="blue">日志条数: {{ debugLogs.length }}</a-tag>
            </template>

            <div class="debug-log-container" ref="logContainer">
              <div 
                v-for="(log, index) in debugLogs" 
                :key="index"
                class="debug-log-item"
                :class="log.level"
              >
                <div class="log-header">
                  <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                  <a-tag :color="getLogColor(log.level)" size="small">
                    {{ log.level.toUpperCase() }}
                  </a-tag>
                </div>
                <div class="log-message">{{ log.message }}</div>
                <div v-if="log.data" class="log-data">
                  <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
                </div>
              </div>

              <div v-if="debugLogs.length === 0" class="empty-logs">
                <a-empty description="暂无调试日志" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 测试结果 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="测试结果" size="small">
            <div v-if="testResults.length > 0">
              <a-timeline>
                <a-timeline-item 
                  v-for="(result, index) in testResults" 
                  :key="index"
                  :color="result.success ? 'green' : 'red'"
                >
                  <template #dot>
                    <CheckCircleOutlined v-if="result.success" style="color: #52c41a" />
                    <CloseCircleOutlined v-else style="color: #f5222d" />
                  </template>
                  
                  <div class="result-item">
                    <div class="result-title">{{ result.title }}</div>
                    <div class="result-desc">{{ result.description }}</div>
                    <div v-if="result.error" class="result-error">
                      错误: {{ result.error }}
                    </div>
                    <div class="result-time">
                      耗时: {{ result.duration }}ms
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
            
            <a-empty v-else description="暂无测试结果" />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  BugOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import { useChatStore } from '@/stores/chatStore'
import { siliconflowApi } from '@/api/siliconflow'

// 响应式数据
const testMessage = ref('@导演 请帮我规划一个令人兴奋的科幻故事')
const testing = ref(false)
const testingHttp = ref(false)
const testingStore = ref(false)
const testStatus = ref('ready')
const debugLogs = ref<any[]>([])
const testResults = ref<any[]>([])
const logContainer = ref<HTMLElement>()

// Store
const chatStore = useChatStore()

// 方法
const addDebugLog = (level: string, message: string, data?: any) => {
  debugLogs.value.push({
    level,
    message,
    data,
    timestamp: Date.now()
  })
  
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

const addTestResult = (title: string, description: string, success: boolean, duration: number, error?: string) => {
  testResults.value.unshift({
    title,
    description,
    success,
    duration,
    error,
    timestamp: Date.now()
  })
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

const getLogColor = (level: string) => {
  const colors = {
    info: 'blue',
    success: 'green',
    warning: 'orange',
    error: 'red'
  }
  return colors[level] || 'default'
}

const testMentionParsing = () => {
  const startTime = Date.now()
  addDebugLog('info', '开始测试@mention解析...')
  
  try {
    const mentions = chatStore.parseMentions(testMessage.value)
    addDebugLog('success', '解析成功', { mentions, originalMessage: testMessage.value })
    
    const duration = Date.now() - startTime
    addTestResult(
      '@mention解析测试',
      `成功解析出${mentions.length}个mention`,
      true,
      duration
    )
    
    message.success('@mention解析测试成功')
    
  } catch (error) {
    addDebugLog('error', '解析失败', { error: error.message })
    
    const duration = Date.now() - startTime
    addTestResult(
      '@mention解析测试',
      '解析失败',
      false,
      duration,
      error.message
    )
    
    message.error('@mention解析测试失败')
  }
}

const testHttpApi = async () => {
  testingHttp.value = true
  const startTime = Date.now()
  addDebugLog('info', '开始测试HTTP API...')
  
  try {
    // 测试Silicon Flow API连接
    addDebugLog('info', '测试API连接...')
    const connectionTest = await siliconflowApi.testConnection()
    addDebugLog('info', 'API连接测试结果', connectionTest.data)
    
    if (!connectionTest.data.success) {
      throw new Error('API连接失败')
    }
    
    // 测试创意构思API
    addDebugLog('info', '测试创意构思API...')
    const creativeTest = await siliconflowApi.generateCreativeIdea({
      topic: '科幻故事规划',
      context: '这是一个@导演功能的测试'
    })
    
    addDebugLog('success', '创意构思API测试结果', creativeTest.data)
    
    const duration = Date.now() - startTime
    addTestResult(
      'HTTP API测试',
      'Silicon Flow API调用成功',
      creativeTest.data.success,
      duration,
      creativeTest.data.success ? undefined : creativeTest.data.error
    )
    
    if (creativeTest.data.success) {
      message.success('HTTP API测试成功')
    } else {
      message.error('HTTP API测试失败')
    }
    
  } catch (error) {
    addDebugLog('error', 'HTTP API测试失败', { error: error.message })
    
    const duration = Date.now() - startTime
    addTestResult(
      'HTTP API测试',
      'API调用失败',
      false,
      duration,
      error.message
    )
    
    message.error('HTTP API测试失败')
  } finally {
    testingHttp.value = false
  }
}

const testChatStore = async () => {
  testingStore.value = true
  const startTime = Date.now()
  addDebugLog('info', '开始测试ChatStore...')
  
  try {
    // 解析mentions
    const mentions = chatStore.parseMentions(testMessage.value)
    addDebugLog('info', '解析mentions', mentions)
    
    // 测试HTTP API mention处理
    addDebugLog('info', '测试HTTP API mention处理...')
    const success = await chatStore.tryHttpApiMention(mentions, testMessage.value)
    
    const duration = Date.now() - startTime
    addTestResult(
      'ChatStore测试',
      'HTTP API mention处理',
      success,
      duration,
      success ? undefined : '处理失败'
    )
    
    if (success) {
      addDebugLog('success', 'ChatStore测试成功')
      message.success('ChatStore测试成功')
    } else {
      addDebugLog('warning', 'ChatStore HTTP API失败，但这是正常的降级行为')
      message.warning('ChatStore HTTP API失败')
    }
    
  } catch (error) {
    addDebugLog('error', 'ChatStore测试失败', { error: error.message })
    
    const duration = Date.now() - startTime
    addTestResult(
      'ChatStore测试',
      '测试失败',
      false,
      duration,
      error.message
    )
    
    message.error('ChatStore测试失败')
  } finally {
    testingStore.value = false
  }
}

const runDebugTest = async () => {
  testing.value = true
  testStatus.value = 'testing'
  
  addDebugLog('info', '开始完整调试测试...')
  
  try {
    // 1. 测试mention解析
    testMentionParsing()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 2. 测试HTTP API
    await testHttpApi()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 3. 测试ChatStore
    await testChatStore()
    
    testStatus.value = 'success'
    addDebugLog('success', '完整调试测试完成')
    message.success('调试测试完成')
    
  } catch (error) {
    testStatus.value = 'error'
    addDebugLog('error', '调试测试失败', { error: error.message })
    message.error('调试测试失败')
  } finally {
    testing.value = false
  }
}

const quickTest = (msg: string) => {
  testMessage.value = msg
  runDebugTest()
}

const clearDebugLog = () => {
  debugLogs.value = []
  testResults.value = []
  testStatus.value = 'ready'
  message.success('调试日志已清空')
}
</script>

<style scoped>
.mention-debug {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.debug-content {
  max-width: 1400px;
  margin: 0 auto;
}

.debug-log-container {
  height: 400px;
  overflow-y: auto;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.debug-log-item {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #d9d9d9;
}

.debug-log-item.info {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.debug-log-item.success {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.debug-log-item.warning {
  background: #fffbe6;
  border-left-color: #faad14;
}

.debug-log-item.error {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.log-time {
  color: #999;
  font-size: 11px;
}

.log-message {
  color: #262626;
  margin-bottom: 4px;
}

.log-data {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.result-item {
  padding: 8px 0;
}

.result-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.result-desc {
  color: #666;
  margin-bottom: 4px;
}

.result-error {
  color: #f5222d;
  font-size: 12px;
  margin-bottom: 4px;
}

.result-time {
  color: #999;
  font-size: 12px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
