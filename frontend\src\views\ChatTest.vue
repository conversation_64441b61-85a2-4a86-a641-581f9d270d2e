<template>
  <div class="chat-test">
    <a-page-header
      title="聊天功能测试"
      sub-title="测试@导演功能的完整数据流"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag :color="testStatus === 'success' ? 'green' : testStatus === 'error' ? 'red' : 'blue'">
            测试状态: {{ testStatus }}
          </a-tag>
          <a-button @click="runFullTest" :loading="testing">
            <template #icon><PlayCircleOutlined /></template>
            运行完整测试
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <a-row :gutter="16">
        <!-- 左侧：测试控制 -->
        <a-col :span="8">
          <a-card title="测试控制" size="small">
            <a-space direction="vertical" style="width: 100%">
              <a-button 
                block
                @click="testAddMessage"
                type="primary"
              >
                测试添加消息
              </a-button>
              
              <a-button 
                block
                @click="testMentionParsing"
              >
                测试@mention解析
              </a-button>
              
              <a-button 
                block
                @click="testDirectorCall"
                :loading="testingDirector"
              >
                测试@导演调用
              </a-button>
              
              <a-button 
                block
                @click="testFallbackResponse"
              >
                测试本地响应
              </a-button>
              
              <a-button 
                block
                @click="clearAllMessages"
                danger
              >
                清空所有消息
              </a-button>
            </a-space>

            <a-divider>状态信息</a-divider>
            
            <a-descriptions size="small" :column="1">
              <a-descriptions-item label="消息总数">
                {{ chatStore.messageCount }}
              </a-descriptions-item>
              <a-descriptions-item label="加载状态">
                {{ chatStore.isLoading ? '加载中' : '空闲' }}
              </a-descriptions-item>
              <a-descriptions-item label="Socket状态">
                {{ socketStore.connectionStatus }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 中间：消息列表 -->
        <a-col :span="10">
          <a-card title="消息列表" size="small">
            <template #extra>
              <a-tag color="blue">{{ messages.length }} 条消息</a-tag>
            </template>

            <div class="message-list" ref="messageList">
              <div 
                v-for="message in messages" 
                :key="message.id"
                class="message-item"
                :class="message.type"
              >
                <a-card size="small" :class="['message-card', message.type]">
                  <template #title>
                    <div class="message-header">
                      <a-avatar size="small" :style="{ backgroundColor: getAvatarColor(message.type) }">
                        {{ getAvatarText(message.sender) }}
                      </a-avatar>
                      <span class="sender">{{ message.sender }}</span>
                      <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
                    </div>
                  </template>
                  <div class="message-content">{{ message.content }}</div>
                </a-card>
              </div>

              <div v-if="messages.length === 0" class="empty-messages">
                <a-empty description="暂无消息" />
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：调试日志 -->
        <a-col :span="6">
          <a-card title="调试日志" size="small">
            <template #extra>
              <a-button size="small" @click="clearLogs">清空日志</a-button>
            </template>

            <div class="debug-logs" ref="debugLogs">
              <div 
                v-for="(log, index) in debugLogs" 
                :key="index"
                class="log-item"
                :class="log.level"
              >
                <div class="log-time">{{ formatTime(log.timestamp) }}</div>
                <div class="log-message">{{ log.message }}</div>
              </div>

              <div v-if="debugLogs.length === 0" class="empty-logs">
                <a-empty description="暂无日志" size="small" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 输入测试区域 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="输入测试" size="small">
            <a-input-group compact>
              <a-input
                v-model:value="testInput"
                placeholder="输入测试消息，如：@导演 请帮我规划一个科幻故事"
                style="width: calc(100% - 100px)"
                @press-enter="sendTestMessage"
              />
              <a-button 
                type="primary" 
                @click="sendTestMessage"
                :loading="testingDirector"
                :disabled="!testInput.trim()"
              >
                发送
              </a-button>
            </a-input-group>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import { useChatStore } from '@/stores/chatStore'
import { useSocketStore } from '@/stores/socketStore'

// 响应式数据
const testing = ref(false)
const testingDirector = ref(false)
const testStatus = ref('ready')
const testInput = ref('@导演 请帮我规划一个科幻故事')
const debugLogs = ref<any[]>([])
const messageList = ref<HTMLElement>()
const debugLogsRef = ref<HTMLElement>()

// Store
const chatStore = useChatStore()
const socketStore = useSocketStore()

// 计算属性
const messages = computed(() => chatStore.messages)

// 方法
const addDebugLog = (level: string, message: string) => {
  debugLogs.value.push({
    level,
    message,
    timestamp: Date.now()
  })
  
  nextTick(() => {
    if (debugLogsRef.value) {
      debugLogsRef.value.scrollTop = debugLogsRef.value.scrollHeight
    }
  })
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getAvatarColor = (type: string) => {
  const colorMap: Record<string, string> = {
    user: '#1890ff',
    agent: '#52c41a',
    system: '#fa8c16'
  }
  return colorMap[type] || '#d9d9d9'
}

const getAvatarText = (sender: string) => {
  if (sender === '用户') return '我'
  if (sender === '系统') return '系'
  return sender.charAt(0)
}

const testAddMessage = () => {
  addDebugLog('info', '测试添加消息功能')
  
  chatStore.addMessage({
    sender: '测试',
    content: '这是一条测试消息，时间：' + new Date().toLocaleTimeString(),
    type: 'system'
  })
  
  addDebugLog('success', '测试消息已添加')
  message.success('测试消息已添加')
}

const testMentionParsing = () => {
  addDebugLog('info', '测试@mention解析功能')
  
  const testText = '@导演 请帮我规划一个科幻故事'
  const mentions = chatStore.parseMentions(testText)
  
  addDebugLog('success', `解析结果：${JSON.stringify(mentions)}`)
  message.success(`解析到 ${mentions.length} 个mention`)
}

const testDirectorCall = async () => {
  testingDirector.value = true
  addDebugLog('info', '开始测试@导演调用')
  
  try {
    const mentions = chatStore.parseMentions('@导演 请帮我规划一个科幻故事')
    const success = await chatStore.tryHttpApiMention(mentions, '@导演 请帮我规划一个科幻故事')
    
    if (success) {
      addDebugLog('success', '@导演调用成功')
      message.success('@导演调用成功')
    } else {
      addDebugLog('warning', '@导演调用失败，但这可能是正常的')
      message.warning('@导演调用失败')
    }
  } catch (error) {
    addDebugLog('error', `@导演调用异常：${error.message}`)
    message.error('@导演调用异常')
  } finally {
    testingDirector.value = false
  }
}

const testFallbackResponse = () => {
  addDebugLog('info', '测试本地备用响应')
  
  const fallbackContent = chatStore.generateFallbackResponse('导演', '测试本地响应功能')
  
  chatStore.addMessage({
    sender: '导演',
    content: fallbackContent,
    type: 'agent',
    agentType: '导演'
  })
  
  addDebugLog('success', '本地响应已生成')
  message.success('本地响应已生成')
}

const sendTestMessage = async () => {
  if (!testInput.value.trim()) return
  
  addDebugLog('info', `发送测试消息：${testInput.value}`)
  
  try {
    chatStore.sendMessage(testInput.value)
    addDebugLog('success', '消息发送成功')
    testInput.value = ''
  } catch (error) {
    addDebugLog('error', `消息发送失败：${error.message}`)
    message.error('消息发送失败')
  }
}

const clearAllMessages = () => {
  chatStore.clearMessages()
  addDebugLog('info', '所有消息已清空')
  message.success('所有消息已清空')
}

const clearLogs = () => {
  debugLogs.value = []
  message.success('调试日志已清空')
}

const runFullTest = async () => {
  testing.value = true
  testStatus.value = 'testing'
  
  try {
    addDebugLog('info', '开始完整测试流程')
    
    // 1. 测试添加消息
    testAddMessage()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 2. 测试mention解析
    testMentionParsing()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 3. 测试本地响应
    testFallbackResponse()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 4. 测试@导演调用
    await testDirectorCall()
    
    testStatus.value = 'success'
    addDebugLog('success', '完整测试流程完成')
    message.success('完整测试流程完成')
  } catch (error) {
    testStatus.value = 'error'
    addDebugLog('error', `测试流程失败：${error.message}`)
    message.error('测试流程失败')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.chat-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.message-list {
  height: 400px;
  overflow-y: auto;
  padding: 12px;
}

.message-item {
  margin-bottom: 12px;
}

.message-card {
  border-radius: 8px;
}

.message-card.user {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.message-card.agent {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.message-card.system {
  background: #fff7e6;
  border-color: #ffd591;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.sender {
  font-weight: 500;
  color: #262626;
}

.timestamp {
  color: #8c8c8c;
  margin-left: auto;
}

.message-content {
  line-height: 1.6;
  color: #262626;
  margin-top: 4px;
  white-space: pre-wrap;
}

.debug-logs {
  height: 400px;
  overflow-y: auto;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #d9d9d9;
}

.log-item.info {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.log-item.success {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.log-item.warning {
  background: #fffbe6;
  border-left-color: #faad14;
}

.log-item.error {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.log-time {
  color: #999;
  font-size: 11px;
  margin-bottom: 2px;
}

.log-message {
  color: #262626;
}

.empty-messages,
.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
