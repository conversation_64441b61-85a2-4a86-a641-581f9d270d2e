<template>
  <div class="timeout-test">
    <a-page-header
      title="超时处理测试"
      sub-title="测试API超时和错误处理机制"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag :color="testStatus === 'success' ? 'green' : testStatus === 'error' ? 'red' : 'blue'">
            状态: {{ testStatus }}
          </a-tag>
          <a-button @click="runAllTests" :loading="testing">
            <template #icon><ClockCircleOutlined /></template>
            运行所有测试
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <a-row :gutter="16">
        <!-- 左侧：测试控制 -->
        <a-col :span="12">
          <a-card title="超时测试" size="small">
            <a-space direction="vertical" style="width: 100%">
              <a-button 
                block
                @click="testNormalApi"
                :loading="testingNormal"
                type="primary"
              >
                测试正常API调用
              </a-button>
              
              <a-button 
                block
                @click="testSlowApi"
                :loading="testingSlow"
              >
                测试慢速API调用
              </a-button>
              
              <a-button 
                block
                @click="testTimeoutApi"
                :loading="testingTimeout"
              >
                测试超时API调用
              </a-button>
              
              <a-button 
                block
                @click="testFallbackResponse"
                :loading="testingFallback"
              >
                测试本地备用响应
              </a-button>
              
              <a-divider>配置测试</a-divider>
              
              <a-form-item label="自定义超时时间(秒)">
                <a-input-number 
                  v-model:value="customTimeout" 
                  :min="1" 
                  :max="300"
                  style="width: 100%"
                />
              </a-form-item>
              
              <a-button 
                block
                @click="testCustomTimeout"
                :loading="testingCustom"
              >
                测试自定义超时
              </a-button>
            </a-space>
          </a-card>
        </a-col>

        <!-- 右侧：测试结果 -->
        <a-col :span="12">
          <a-card title="测试结果" size="small">
            <template #extra>
              <a-button size="small" @click="clearResults">清空结果</a-button>
            </template>

            <div class="results-container" ref="resultsContainer">
              <a-timeline>
                <a-timeline-item 
                  v-for="(result, index) in testResults" 
                  :key="index"
                  :color="getResultColor(result.status)"
                >
                  <template #dot>
                    <CheckCircleOutlined v-if="result.status === 'success'" style="color: #52c41a" />
                    <CloseCircleOutlined v-if="result.status === 'error'" style="color: #f5222d" />
                    <ClockCircleOutlined v-if="result.status === 'timeout'" style="color: #faad14" />
                    <LoadingOutlined v-if="result.status === 'loading'" style="color: #1890ff" />
                  </template>
                  
                  <div class="result-item">
                    <div class="result-title">{{ result.title }}</div>
                    <div class="result-desc">{{ result.description }}</div>
                    <div v-if="result.error" class="result-error">
                      错误: {{ result.error }}
                    </div>
                    <div class="result-time">
                      {{ formatTime(result.timestamp) }} | 耗时: {{ result.duration }}ms
                    </div>
                  </div>
                </a-timeline-item>
              </a-timeline>

              <div v-if="testResults.length === 0" class="empty-results">
                <a-empty description="暂无测试结果" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时状态监控 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="实时状态监控" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic 
                  title="成功测试" 
                  :value="successCount" 
                  :value-style="{ color: '#3f8600' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic 
                  title="失败测试" 
                  :value="errorCount" 
                  :value-style="{ color: '#cf1322' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic 
                  title="超时测试" 
                  :value="timeoutCount" 
                  :value-style="{ color: '#d48806' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic 
                  title="平均响应时间" 
                  :value="averageResponseTime" 
                  suffix="ms"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import { siliconflowApi } from '@/api/siliconflow'
import { useChatStore } from '@/stores/chatStore'

// 响应式数据
const testing = ref(false)
const testingNormal = ref(false)
const testingSlow = ref(false)
const testingTimeout = ref(false)
const testingFallback = ref(false)
const testingCustom = ref(false)
const testStatus = ref('ready')
const customTimeout = ref(30)
const testResults = ref<any[]>([])
const resultsContainer = ref<HTMLElement>()

// Store
const chatStore = useChatStore()

// 计算属性
const successCount = computed(() => testResults.value.filter(r => r.status === 'success').length)
const errorCount = computed(() => testResults.value.filter(r => r.status === 'error').length)
const timeoutCount = computed(() => testResults.value.filter(r => r.status === 'timeout').length)
const averageResponseTime = computed(() => {
  const completedTests = testResults.value.filter(r => r.duration > 0)
  if (completedTests.length === 0) return 0
  const total = completedTests.reduce((sum, r) => sum + r.duration, 0)
  return Math.round(total / completedTests.length)
})

// 方法
const addTestResult = (title: string, description: string, status: string, duration: number = 0, error?: string) => {
  testResults.value.unshift({
    title,
    description,
    status,
    duration,
    error,
    timestamp: Date.now()
  })
  
  nextTick(() => {
    if (resultsContainer.value) {
      resultsContainer.value.scrollTop = 0
    }
  })
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getResultColor = (status: string) => {
  const colors = {
    success: 'green',
    error: 'red',
    timeout: 'orange',
    loading: 'blue'
  }
  return colors[status] || 'default'
}

const testNormalApi = async () => {
  testingNormal.value = true
  const startTime = Date.now()
  
  addTestResult('正常API测试', '开始测试...', 'loading')
  
  try {
    const response = await siliconflowApi.testConnection()
    const duration = Date.now() - startTime
    
    if (response.data.success) {
      addTestResult('正常API测试', '连接测试成功', 'success', duration)
      message.success('正常API测试成功')
    } else {
      addTestResult('正常API测试', '连接测试失败', 'error', duration, response.data.error)
      message.error('正常API测试失败')
    }
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('正常API测试', '请求异常', 'error', duration, error.message)
    message.error('正常API测试异常')
  } finally {
    testingNormal.value = false
  }
}

const testSlowApi = async () => {
  testingSlow.value = true
  const startTime = Date.now()
  
  addTestResult('慢速API测试', '测试创意构思API...', 'loading')
  
  try {
    const response = await siliconflowApi.generateCreativeIdea({
      topic: '复杂的科幻故事构思，需要深度思考和详细规划',
      context: '这是一个慢速API测试，用于验证长时间响应的处理'
    })
    
    const duration = Date.now() - startTime
    
    if (response.data.success) {
      addTestResult('慢速API测试', '创意构思成功', 'success', duration)
      message.success('慢速API测试成功')
    } else {
      addTestResult('慢速API测试', '创意构思失败', 'error', duration, response.data.error)
      message.error('慢速API测试失败')
    }
  } catch (error) {
    const duration = Date.now() - startTime
    
    if (error.message?.includes('timeout')) {
      addTestResult('慢速API测试', '请求超时', 'timeout', duration, error.message)
      message.warning('慢速API测试超时')
    } else {
      addTestResult('慢速API测试', '请求异常', 'error', duration, error.message)
      message.error('慢速API测试异常')
    }
  } finally {
    testingSlow.value = false
  }
}

const testTimeoutApi = async () => {
  testingTimeout.value = true
  const startTime = Date.now()
  
  addTestResult('超时API测试', '故意触发超时...', 'loading')
  
  try {
    // 使用很短的超时时间来故意触发超时
    const response = await fetch('/api/siliconflow/creative', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: '超时测试',
        context: '这是一个超时测试'
      }),
      signal: AbortSignal.timeout(1000) // 1秒超时
    })
    
    const duration = Date.now() - startTime
    addTestResult('超时API测试', '意外成功', 'success', duration)
    
  } catch (error) {
    const duration = Date.now() - startTime
    
    if (error.name === 'AbortError' || error.message?.includes('timeout')) {
      addTestResult('超时API测试', '成功触发超时', 'timeout', duration, '预期的超时错误')
      message.success('超时测试成功（预期行为）')
    } else {
      addTestResult('超时API测试', '其他错误', 'error', duration, error.message)
      message.error('超时测试异常')
    }
  } finally {
    testingTimeout.value = false
  }
}

const testFallbackResponse = async () => {
  testingFallback.value = true
  const startTime = Date.now()
  
  addTestResult('备用响应测试', '测试本地模拟响应...', 'loading')
  
  try {
    const fallbackContent = chatStore.generateFallbackResponse('导演', '测试备用响应功能')
    const duration = Date.now() - startTime
    
    if (fallbackContent && fallbackContent.length > 0) {
      addTestResult('备用响应测试', '本地响应生成成功', 'success', duration)
      message.success('备用响应测试成功')
    } else {
      addTestResult('备用响应测试', '本地响应生成失败', 'error', duration, '响应内容为空')
      message.error('备用响应测试失败')
    }
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('备用响应测试', '生成异常', 'error', duration, error.message)
    message.error('备用响应测试异常')
  } finally {
    testingFallback.value = false
  }
}

const testCustomTimeout = async () => {
  testingCustom.value = true
  const startTime = Date.now()
  
  addTestResult('自定义超时测试', `使用${customTimeout.value}秒超时...`, 'loading')
  
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), customTimeout.value * 1000)
    
    const response = await fetch('/api/siliconflow/creative', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: '自定义超时测试',
        context: `测试${customTimeout.value}秒超时`
      }),
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    const duration = Date.now() - startTime
    
    if (response.ok) {
      addTestResult('自定义超时测试', '请求成功', 'success', duration)
      message.success('自定义超时测试成功')
    } else {
      addTestResult('自定义超时测试', '请求失败', 'error', duration, `HTTP ${response.status}`)
      message.error('自定义超时测试失败')
    }
    
  } catch (error) {
    const duration = Date.now() - startTime
    
    if (error.name === 'AbortError') {
      addTestResult('自定义超时测试', '触发超时', 'timeout', duration, `${customTimeout.value}秒超时`)
      message.warning('自定义超时测试超时')
    } else {
      addTestResult('自定义超时测试', '请求异常', 'error', duration, error.message)
      message.error('自定义超时测试异常')
    }
  } finally {
    testingCustom.value = false
  }
}

const runAllTests = async () => {
  testing.value = true
  testStatus.value = 'testing'
  
  try {
    await testNormalApi()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    await testFallbackResponse()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    await testTimeoutApi()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    await testSlowApi()
    
    testStatus.value = 'success'
    message.success('所有测试完成')
  } catch (error) {
    testStatus.value = 'error'
    message.error('测试过程中出现异常')
  } finally {
    testing.value = false
  }
}

const clearResults = () => {
  testResults.value = []
  testStatus.value = 'ready'
  message.success('测试结果已清空')
}
</script>

<style scoped>
.timeout-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.results-container {
  height: 400px;
  overflow-y: auto;
  padding: 12px;
}

.result-item {
  padding: 8px 0;
}

.result-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.result-desc {
  color: #666;
  margin-bottom: 4px;
}

.result-error {
  color: #f5222d;
  font-size: 12px;
  margin-bottom: 4px;
}

.result-time {
  color: #999;
  font-size: 12px;
}

.empty-results {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
