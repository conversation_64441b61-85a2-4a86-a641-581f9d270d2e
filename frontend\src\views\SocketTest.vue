<template>
  <div class="socket-test">
    <a-page-header
      title="Socket.IO连接测试"
      sub-title="测试前后端Socket.IO通信"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag :color="getStatusColor()">
            状态: {{ connectionStatus }}
          </a-tag>
          <a-button @click="testConnection" :loading="testing">
            <template #icon><ApiOutlined /></template>
            测试连接
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="连接控制" size="small">
            <a-space direction="vertical" style="width: 100%">
              <a-button block type="primary" @click="connect" :disabled="connectionStatus === 'connected'">
                连接Socket.IO
              </a-button>
              <a-button block @click="disconnect" :disabled="connectionStatus !== 'connected'">
                断开连接
              </a-button>
              <a-button block @click="joinSession" :disabled="connectionStatus !== 'connected'">
                加入测试会话
              </a-button>
              <a-button block @click="sendTestMessage" :disabled="connectionStatus !== 'connected'">
                发送测试消息
              </a-button>
              <a-button block @click="sendMentionTest" :disabled="connectionStatus !== 'connected'">
                测试@导演功能
              </a-button>
            </a-space>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="连接信息" size="small">
            <a-descriptions :column="1" size="small" bordered>
              <a-descriptions-item label="Socket状态">
                <a-tag :color="getStatusColor()">{{ connectionStatus }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="服务器地址">
                http://localhost:8000
              </a-descriptions-item>
              <a-descriptions-item label="Socket路径">
                /socket.io/
              </a-descriptions-item>
              <a-descriptions-item label="传输方式">
                polling, websocket
              </a-descriptions-item>
              <a-descriptions-item label="错误信息">
                {{ error || '无' }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="事件日志" size="small">
            <template #extra>
              <a-button size="small" @click="clearLogs">
                <template #icon><ClearOutlined /></template>
                清空
              </a-button>
            </template>

            <div class="log-container" ref="logContainer">
              <div 
                v-for="(log, index) in logs" 
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-type">{{ log.type.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
                <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
              </div>

              <div v-if="logs.length === 0" class="empty-logs">
                <a-empty description="暂无日志" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ApiOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'
import { useSocketStore } from '@/stores/socketStore'
import { io, Socket } from 'socket.io-client'

// 响应式数据
const testing = ref(false)
const logs = ref<any[]>([])
const logContainer = ref<HTMLElement>()
const testSocket = ref<Socket | null>(null)

// Store
const socketStore = useSocketStore()

// 计算属性
const connectionStatus = computed(() => socketStore.connectionStatus)
const error = computed(() => socketStore.error)

// 方法
const getStatusColor = () => {
  switch (connectionStatus.value) {
    case 'connected': return 'green'
    case 'connecting': return 'orange'
    case 'error': return 'red'
    default: return 'default'
  }
}

const addLog = (type: string, message: string, data?: any) => {
  logs.value.push({
    type,
    message,
    data,
    timestamp: Date.now()
  })
  
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

const testConnection = async () => {
  testing.value = true
  addLog('info', '开始连接测试...')
  
  try {
    // 创建独立的测试连接
    testSocket.value = io('http://localhost:8000', {
      transports: ['polling', 'websocket'],
      timeout: 5000,
      path: '/socket.io/',
      forceNew: true
    })
    
    testSocket.value.on('connect', () => {
      addLog('success', 'Socket.IO连接成功')
      message.success('Socket.IO连接测试成功')
    })
    
    testSocket.value.on('connect_error', (err) => {
      addLog('error', 'Socket.IO连接失败', err)
      message.error('Socket.IO连接测试失败')
    })
    
    testSocket.value.on('connection_confirmed', (data) => {
      addLog('info', '收到连接确认', data)
    })
    
    // 等待连接结果
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('连接超时'))
      }, 10000)
      
      testSocket.value?.on('connect', () => {
        clearTimeout(timeout)
        resolve(true)
      })
      
      testSocket.value?.on('connect_error', (err) => {
        clearTimeout(timeout)
        reject(err)
      })
    })
    
  } catch (error) {
    addLog('error', '连接测试失败: ' + error.message)
    message.error('连接测试失败')
  } finally {
    testing.value = false
    if (testSocket.value) {
      testSocket.value.disconnect()
      testSocket.value = null
    }
  }
}

const connect = () => {
  addLog('info', '尝试连接Socket.IO...')
  socketStore.connect()
  
  // 监听连接事件
  socketStore.socket?.on('connect', () => {
    addLog('success', 'Socket.IO连接成功')
  })
  
  socketStore.socket?.on('connect_error', (err) => {
    addLog('error', 'Socket.IO连接失败', err)
  })
  
  socketStore.socket?.on('disconnect', (reason) => {
    addLog('warning', 'Socket.IO断开连接', { reason })
  })
  
  socketStore.socket?.on('connection_confirmed', (data) => {
    addLog('info', '收到连接确认', data)
  })
  
  socketStore.socket?.on('agent_response', (data) => {
    addLog('success', '收到Agent响应', data)
  })
  
  socketStore.socket?.on('new_message', (data) => {
    addLog('info', '收到新消息', data)
  })
  
  socketStore.socket?.on('error', (data) => {
    addLog('error', '收到错误消息', data)
  })
}

const disconnect = () => {
  addLog('info', '断开Socket.IO连接...')
  socketStore.disconnect()
}

const joinSession = () => {
  if (socketStore.socket?.connected) {
    const storyId = 'test_story'
    const userId = 'test_user'
    
    addLog('info', `加入测试会话: ${storyId}`)
    socketStore.joinStorySession(storyId, userId)
    
    // 监听会话相关事件
    socketStore.socket.on('session_status', (data) => {
      addLog('info', '收到会话状态', data)
    })
    
    socketStore.socket.on('user_joined', (data) => {
      addLog('info', '用户加入会话', data)
    })
  } else {
    message.error('Socket未连接')
  }
}

const sendTestMessage = () => {
  if (socketStore.socket?.connected) {
    const testMsg = 'Hello, this is a test message!'
    addLog('info', `发送测试消息: ${testMsg}`)
    socketStore.sendChatMessage(testMsg, 'test_story', 'test_user')
  } else {
    message.error('Socket未连接')
  }
}

const sendMentionTest = () => {
  if (socketStore.socket?.connected) {
    const mentionMsg = '@导演 请帮我规划一个科幻故事'
    addLog('info', `发送@mention测试: ${mentionMsg}`)
    socketStore.sendChatMessage(mentionMsg, 'test_story', 'test_user')
  } else {
    message.error('Socket未连接')
  }
}

const clearLogs = () => {
  logs.value = []
}

// 生命周期
onMounted(() => {
  addLog('info', '页面加载完成，准备测试Socket.IO连接')
})
</script>

<style scoped>
.socket-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #d9d9d9;
}

.log-item.success {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.log-item.error {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.log-item.warning {
  background: #fffbe6;
  border-left-color: #faad14;
}

.log-item.info {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.log-time {
  color: #999;
  margin-right: 8px;
}

.log-type {
  font-weight: bold;
  margin-right: 8px;
  min-width: 60px;
  display: inline-block;
}

.log-message {
  color: #262626;
}

.log-data {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 11px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  width: 100px;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
