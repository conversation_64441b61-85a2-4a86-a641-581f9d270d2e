import { createRouter, createWebHistory } from 'vue-router'
import ChatLayout from '@/views/ChatLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: ChatLayout
    },
    {
      path: '/chat/:storyId?',
      name: 'chat',
      component: ChatLayout,
      props: true
    },
    {
      path: '/collaboration',
      name: 'collaboration',
      component: () => import('@/views/CollaborationView.vue')
    },
    {
      path: '/memory',
      name: 'memory',
      component: () => import('@/views/MemoryManagement.vue')
    },
    {
      path: '/relationship',
      name: 'relationship',
      component: () => import('@/views/RelationshipNetwork.vue')
    },
    {
      path: '/performance',
      name: 'performance',
      component: () => import('@/views/PerformanceMonitor.vue')
    },
    {
      path: '/personality',
      name: 'personality',
      component: () => import('@/views/PersonalitySettings.vue')
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('@/views/SystemTest.vue')
    },
    {
      path: '/stress',
      name: 'stress',
      component: () => import('@/views/StressTest.vue')
    },
    {
      path: '/ai-collaboration',
      name: 'ai-collaboration',
      component: () => import('@/views/AICollaboration.vue')
    },
    {
      path: '/agent-test',
      name: 'agent-test',
      component: () => import('@/views/AgentInteractionTest.vue')
    },
    {
      path: '/socket-test',
      name: 'socket-test',
      component: () => import('@/views/SocketTest.vue')
    },
    {
      path: '/director-test',
      name: 'director-test',
      component: () => import('@/views/DirectorTest.vue')
    },
    {
      path: '/mention-debug',
      name: 'mention-debug',
      component: () => import('@/views/MentionDebug.vue')
    },
    {
      path: '/timeout-test',
      name: 'timeout-test',
      component: () => import('@/views/TimeoutTest.vue')
    },
    {
      path: '/chat-test',
      name: 'chat-test',
      component: () => import('@/views/ChatTest.vue')
    }
  ]
})

export default router
