<template>
  <div class="optimization-tips">
    <div class="tips-header">
      <a-row :gutter="16" align="middle">
        <a-col :span="12">
          <h5>优化提示</h5>
        </a-col>
        <a-col :span="12" style="text-align: right">
          <a-space>
            <a-button size="small" @click="refreshTips">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
            <a-button size="small" type="primary" @click="showOptimizationModal">
              <template #icon><ToolOutlined /></template>
              执行优化
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <a-divider size="small" />

    <div class="tips-content">
      <a-spin :spinning="loading">
        <div v-if="optimizationTips.length > 0" class="tips-list">
          <a-list
            :data-source="optimizationTips"
            size="small"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-badge
                      :count="index + 1"
                      :number-style="{ backgroundColor: getTipPriorityColor(item.priority) }"
                    />
                  </template>
                  <template #title>
                    <a-space>
                      <span class="tip-title">{{ item.title }}</span>
                      <a-tag
                        :color="getTipPriorityColor(item.priority)"
                        size="small"
                      >
                        {{ getTipPriorityText(item.priority) }}
                      </a-tag>
                    </a-space>
                  </template>
                  <template #description>
                    <div class="tip-description">{{ item.description }}</div>
                    <div class="tip-impact" v-if="item.estimated_improvement">
                      <span class="impact-label">预期提升:</span>
                      <span class="impact-value">{{ item.estimated_improvement }}%</span>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-button
                    size="small"
                    type="link"
                    @click="applyOptimization(item)"
                    :loading="item.applying"
                  >
                    应用
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>

        <div v-else class="no-tips">
          <a-result
            status="success"
            title="系统已优化"
            sub-title="当前没有优化建议"
            size="small"
          >
            <template #icon>
              <CheckCircleOutlined style="color: #52c41a" />
            </template>
          </a-result>
        </div>
      </a-spin>
    </div>

    <a-divider size="small" />

    <div class="quick-actions">
      <h6>快速优化</h6>
      <a-space wrap>
        <a-button
          size="small"
          @click="quickOptimize('memory')"
          :loading="quickOptimizing.memory"
        >
          <template #icon><DatabaseOutlined /></template>
          内存优化
        </a-button>
        <a-button
          size="small"
          @click="quickOptimize('cache')"
          :loading="quickOptimizing.cache"
        >
          <template #icon><ThunderboltOutlined /></template>
          缓存优化
        </a-button>
        <a-button
          size="small"
          @click="quickOptimize('performance')"
          :loading="quickOptimizing.performance"
        >
          <template #icon><RocketOutlined /></template>
          性能优化
        </a-button>
      </a-space>
    </div>

    <!-- 优化执行模态框 -->
    <a-modal
      v-model:open="optimizationModalVisible"
      title="执行系统优化"
      @ok="executeOptimization"
      :confirm-loading="optimizationLoading"
    >
      <a-form layout="vertical">
        <a-form-item label="优化类型">
          <a-checkbox-group v-model:value="selectedOptimizations">
            <a-checkbox value="memory">内存优化</a-checkbox>
            <a-checkbox value="cache">缓存优化</a-checkbox>
            <a-checkbox value="performance">性能优化</a-checkbox>
            <a-checkbox value="database">数据库优化</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="forceOptimization">
            强制执行优化（忽略安全检查）
          </a-checkbox>
        </a-form-item>

        <a-alert
          message="注意"
          description="执行优化可能会暂时影响系统性能，建议在低峰期进行。"
          type="warning"
          show-icon
          style="margin-top: 16px"
        />
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  DatabaseOutlined,
  ThunderboltOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'
import { performanceApi } from '@/api/performance'

// 响应式数据
const loading = ref(false)
const optimizationLoading = ref(false)
const optimizationModalVisible = ref(false)

const optimizationTips = ref<any[]>([])
const selectedOptimizations = ref<string[]>(['memory', 'cache'])
const forceOptimization = ref(false)

const quickOptimizing = reactive({
  memory: false,
  cache: false,
  performance: false
})

// 生命周期
onMounted(() => {
  loadOptimizationTips()
})

// 方法
const loadOptimizationTips = async () => {
  loading.value = true
  try {
    // 模拟优化提示数据
    const mockTips = [
      {
        id: '1',
        title: '清理内存缓存',
        description: '检测到内存使用率较高，建议清理不必要的缓存数据',
        priority: 'high',
        estimated_improvement: 15,
        type: 'memory',
        applying: false
      },
      {
        id: '2',
        title: '优化数据库查询',
        description: '发现慢查询，建议添加索引或优化查询语句',
        priority: 'medium',
        estimated_improvement: 25,
        type: 'database',
        applying: false
      },
      {
        id: '3',
        title: '启用响应压缩',
        description: '启用gzip压缩可以减少网络传输时间',
        priority: 'low',
        estimated_improvement: 10,
        type: 'network',
        applying: false
      }
    ]
    
    optimizationTips.value = mockTips
  } catch (error) {
    console.error('加载优化提示失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshTips = () => {
  loadOptimizationTips()
}

const showOptimizationModal = () => {
  optimizationModalVisible.value = true
}

const executeOptimization = async () => {
  if (selectedOptimizations.value.length === 0) {
    message.error('请选择至少一种优化类型')
    return
  }

  optimizationLoading.value = true
  try {
    for (const type of selectedOptimizations.value) {
      await performanceApi.optimize({
        optimization_type: type as any,
        force: forceOptimization.value
      })
    }

    message.success('优化执行成功')
    optimizationModalVisible.value = false
    
    // 刷新提示
    loadOptimizationTips()
  } catch (error) {
    message.error('优化执行失败: ' + error.message)
  } finally {
    optimizationLoading.value = false
  }
}

const applyOptimization = async (tip: any) => {
  tip.applying = true
  try {
    await performanceApi.optimize({
      optimization_type: tip.type,
      force: false
    })

    message.success(`${tip.title} 优化完成`)
    
    // 从列表中移除已应用的提示
    const index = optimizationTips.value.findIndex(t => t.id === tip.id)
    if (index > -1) {
      optimizationTips.value.splice(index, 1)
    }
  } catch (error) {
    message.error(`${tip.title} 优化失败: ` + error.message)
  } finally {
    tip.applying = false
  }
}

const quickOptimize = async (type: string) => {
  quickOptimizing[type] = true
  try {
    await performanceApi.optimize({
      optimization_type: type as any,
      force: false
    })

    message.success(`${getOptimizationTypeName(type)} 完成`)
    
    // 刷新提示
    loadOptimizationTips()
  } catch (error) {
    message.error(`${getOptimizationTypeName(type)} 失败: ` + error.message)
  } finally {
    quickOptimizing[type] = false
  }
}

// 辅助函数
const getTipPriorityColor = (priority: string) => {
  const colorMap = {
    'critical': '#f5222d',
    'high': '#fa8c16',
    'medium': '#faad14',
    'low': '#52c41a'
  }
  return colorMap[priority] || '#1890ff'
}

const getTipPriorityText = (priority: string) => {
  const textMap = {
    'critical': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[priority] || priority
}

const getOptimizationTypeName = (type: string) => {
  const nameMap = {
    'memory': '内存优化',
    'cache': '缓存优化',
    'performance': '性能优化',
    'database': '数据库优化'
  }
  return nameMap[type] || type
}
</script>

<style scoped>
.optimization-tips {
  padding: 8px;
}

.tips-header h5 {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.tips-list {
  max-height: 200px;
  overflow-y: auto;
}

.tip-title {
  font-weight: 500;
  color: #262626;
  font-size: 13px;
}

.tip-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 4px;
}

.tip-impact {
  font-size: 11px;
}

.impact-label {
  color: #999;
  margin-right: 4px;
}

.impact-value {
  color: #52c41a;
  font-weight: 500;
}

.no-tips {
  text-align: center;
  padding: 20px;
}

.quick-actions {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
}

.quick-actions h6 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 12px;
}

:deep(.ant-list-item) {
  padding: 8px 0;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
}

:deep(.ant-list-item-meta-description) {
  font-size: 11px;
}

:deep(.ant-result-title) {
  font-size: 14px;
}

:deep(.ant-result-subtitle) {
  font-size: 11px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-alert) {
  font-size: 12px;
}
</style>
