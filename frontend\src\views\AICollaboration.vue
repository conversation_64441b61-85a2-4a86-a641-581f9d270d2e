<template>
  <div class="ai-collaboration">
    <a-page-header
      title="AI智能协作"
      sub-title="体验真实的Silicon Flow AI模型协作创作"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="testConnection" :loading="testingConnection">
            <template #icon><ApiOutlined /></template>
            测试连接
          </a-button>
          <a-button type="primary" @click="startCollaboration" :loading="collaborating">
            <template #icon><TeamOutlined /></template>
            开始协作
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="collaboration-content">
      <!-- 连接状态 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="Silicon Flow API状态" size="small">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic
                  title="连接状态"
                  :value="connectionStatus"
                  :value-style="{ color: getConnectionColor() }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="API密钥"
                  :value="apiKeyStatus"
                  :value-style="{ color: getApiKeyColor() }"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="最后测试"
                  :value="lastTestTime"
                  :value-style="{ color: '#666' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 协作任务配置 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="12">
          <a-card title="协作任务配置" size="small">
            <a-form layout="vertical">
              <a-form-item label="任务类型">
                <a-select v-model:value="taskConfig.type" @change="onTaskTypeChange">
                  <a-select-option value="creative">创意构思</a-select-option>
                  <a-select-option value="character">角色设计</a-select-option>
                  <a-select-option value="plot">剧情规划</a-select-option>
                  <a-select-option value="text">文本生成</a-select-option>
                  <a-select-option value="discussion">多角色讨论</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="任务描述">
                <a-textarea
                  v-model:value="taskConfig.description"
                  :placeholder="getTaskPlaceholder()"
                  :rows="4"
                />
              </a-form-item>

              <a-form-item label="参与角色" v-if="taskConfig.type === 'discussion'">
                <a-checkbox-group v-model:value="taskConfig.participants">
                  <a-checkbox value="director">导演</a-checkbox>
                  <a-checkbox value="designer">设计师</a-checkbox>
                  <a-checkbox value="writer">撰写师</a-checkbox>
                </a-checkbox-group>
              </a-form-item>

              <a-form-item label="风格要求" v-if="taskConfig.type === 'character' || taskConfig.type === 'text'">
                <a-input
                  v-model:value="taskConfig.style"
                  placeholder="例如：现代简约、古典优雅、科幻未来..."
                />
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="AI模型配置" size="small">
            <a-descriptions :column="1" size="small" bordered>
              <a-descriptions-item label="导演模型">
                DeepSeek-R1-0528-Qwen3-8B
              </a-descriptions-item>
              <a-descriptions-item label="设计师模型">
                Qwen2.5-72B-Instruct
              </a-descriptions-item>
              <a-descriptions-item label="撰写师模型">
                deepseek-chat
              </a-descriptions-item>
              <a-descriptions-item label="温度参数">
                0.7 (平衡创意与一致性)
              </a-descriptions-item>
              <a-descriptions-item label="最大Token">
                1000 (适中长度响应)
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 协作结果 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="协作结果" size="small">
            <template #extra>
              <a-space>
                <a-tag v-if="collaborationResult" color="green">
                  耗时: {{ collaborationTime }}ms
                </a-tag>
                <a-button size="small" @click="clearResults">
                  <template #icon><ClearOutlined /></template>
                  清空
                </a-button>
                <a-button size="small" @click="exportResults" :disabled="!collaborationResult">
                  <template #icon><DownloadOutlined /></template>
                  导出
                </a-button>
              </a-space>
            </template>

            <div v-if="collaborating" class="collaboration-loading">
              <a-spin size="large">
                <template #indicator>
                  <LoadingOutlined style="font-size: 24px" spin />
                </template>
              </a-spin>
              <p>AI正在协作创作中，请稍候...</p>
            </div>

            <div v-else-if="collaborationResult" class="collaboration-results">
              <!-- 单角色结果 -->
              <div v-if="taskConfig.type !== 'discussion'" class="single-result">
                <a-descriptions :column="2" size="small" bordered>
                  <a-descriptions-item label="角色">
                    {{ getRoleName(collaborationResult.role) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="模型">
                    {{ collaborationResult.model }}
                  </a-descriptions-item>
                  <a-descriptions-item label="生成时间">
                    {{ formatTime(collaborationResult.timestamp) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="响应状态">
                    <a-tag color="green">成功</a-tag>
                  </a-descriptions-item>
                </a-descriptions>

                <a-divider>AI创作内容</a-divider>
                <div class="ai-content">
                  <pre>{{ collaborationResult.content }}</pre>
                </div>
              </div>

              <!-- 多角色讨论结果 -->
              <div v-else class="discussion-results">
                <a-tabs>
                  <a-tab-pane
                    v-for="(result, role) in collaborationResult.results"
                    :key="role"
                    :tab="getRoleName(role)"
                  >
                    <a-descriptions :column="2" size="small" bordered>
                      <a-descriptions-item label="模型">
                        {{ result.data?.model || 'Unknown' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="生成时间">
                        {{ formatTime(result.timestamp) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="响应状态">
                        <a-tag :color="result.success ? 'green' : 'red'">
                          {{ result.success ? '成功' : '失败' }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="角色特色">
                        {{ getRoleDescription(role) }}
                      </a-descriptions-item>
                    </a-descriptions>

                    <a-divider>{{ getRoleName(role) }}的观点</a-divider>
                    <div class="ai-content">
                      <pre v-if="result.success">{{ result.data?.choices?.[0]?.message?.content || '无内容' }}</pre>
                      <a-alert v-else :message="result.error" type="error" />
                    </div>
                  </a-tab-pane>
                </a-tabs>
              </div>
            </div>

            <a-empty v-else description="点击开始协作按钮体验AI智能创作" />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ApiOutlined,
  TeamOutlined,
  ClearOutlined,
  DownloadOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import { siliconflowApi } from '@/api/siliconflow'

// 响应式数据
const testingConnection = ref(false)
const collaborating = ref(false)
const connectionStatus = ref('未测试')
const apiKeyStatus = ref('未验证')
const lastTestTime = ref('--')
const collaborationResult = ref<any>(null)
const collaborationTime = ref(0)

const taskConfig = reactive({
  type: 'creative',
  description: '',
  participants: ['director', 'designer', 'writer'],
  style: ''
})

// 计算属性
const getConnectionColor = () => {
  if (connectionStatus.value === '已连接') return '#52c41a'
  if (connectionStatus.value === '连接失败') return '#f5222d'
  return '#faad14'
}

const getApiKeyColor = () => {
  if (apiKeyStatus.value === '有效') return '#52c41a'
  if (apiKeyStatus.value === '无效') return '#f5222d'
  return '#faad14'
}

// 方法
const testConnection = async () => {
  testingConnection.value = true
  try {
    const response = await siliconflowApi.testConnection()
    
    if (response.data.success) {
      connectionStatus.value = '已连接'
      apiKeyStatus.value = response.data.data.connection_test ? '有效' : '无效'
      lastTestTime.value = new Date().toLocaleTimeString('zh-CN')
      message.success('Silicon Flow API连接测试成功')
    } else {
      connectionStatus.value = '连接失败'
      apiKeyStatus.value = '无效'
      message.error('Silicon Flow API连接测试失败')
    }
  } catch (error) {
    connectionStatus.value = '连接失败'
    apiKeyStatus.value = '无效'
    message.error('连接测试失败: ' + error.message)
  } finally {
    testingConnection.value = false
  }
}

const startCollaboration = async () => {
  if (!taskConfig.description.trim()) {
    message.error('请输入任务描述')
    return
  }

  collaborating.value = true
  const startTime = Date.now()
  
  try {
    let result
    
    switch (taskConfig.type) {
      case 'creative':
        result = await siliconflowApi.generateCreativeIdea({
          topic: taskConfig.description,
          context: '这是一个AI协作创作任务'
        })
        break
        
      case 'character':
        result = await siliconflowApi.designCharacter({
          character_info: taskConfig.description,
          style_requirements: taskConfig.style
        })
        break
        
      case 'plot':
        result = await siliconflowApi.planPlot({
          story_outline: taskConfig.description,
          requirements: taskConfig.style
        })
        break
        
      case 'text':
        result = await siliconflowApi.generateText({
          content_type: '创意文本',
          requirements: taskConfig.description,
          style: taskConfig.style
        })
        break
        
      case 'discussion':
        result = await siliconflowApi.multiAgentDiscussion({
          topic: taskConfig.description,
          participants: taskConfig.participants,
          context: '多角色协作讨论'
        })
        break
        
      default:
        throw new Error('未知的任务类型')
    }
    
    collaborationTime.value = Date.now() - startTime
    
    if (result.data.success) {
      collaborationResult.value = result.data.data
      message.success('AI协作完成！')
    } else {
      throw new Error(result.data.error || '协作失败')
    }
    
  } catch (error) {
    message.error('协作失败: ' + error.message)
  } finally {
    collaborating.value = false
  }
}

const onTaskTypeChange = () => {
  taskConfig.description = ''
  taskConfig.style = ''
}

const getTaskPlaceholder = () => {
  const placeholders = {
    creative: '请描述你想要的创意主题，例如：一个关于时间旅行的科幻故事...',
    character: '请描述角色的基本信息，例如：一个勇敢的女战士，有着神秘的过去...',
    plot: '请提供故事大纲，例如：主角发现了一个古老的秘密，必须阻止邪恶势力...',
    text: '请描述要生成的文本类型和要求，例如：写一段优美的景色描写...',
    discussion: '请提供讨论主题，例如：如何设计一个引人入胜的故事开头...'
  }
  return placeholders[taskConfig.type] || '请输入任务描述...'
}

const clearResults = () => {
  collaborationResult.value = null
  collaborationTime.value = 0
}

const exportResults = () => {
  if (!collaborationResult.value) return
  
  const data = {
    taskType: taskConfig.type,
    taskDescription: taskConfig.description,
    result: collaborationResult.value,
    timestamp: new Date().toISOString(),
    collaborationTime: collaborationTime.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ai-collaboration-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('协作结果已导出')
}

const getRoleName = (role: string) => {
  const names = {
    director: '导演',
    designer: '设计师',
    writer: '撰写师'
  }
  return names[role] || role
}

const getRoleDescription = (role: string) => {
  const descriptions = {
    director: '统筹规划，艺术指导',
    designer: '视觉设计，创意构思',
    writer: '文本创作，剧情编写'
  }
  return descriptions[role] || ''
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style scoped>
.ai-collaboration {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.collaboration-content {
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.collaboration-loading {
  text-align: center;
  padding: 40px;
}

.collaboration-loading p {
  margin-top: 16px;
  color: #666;
}

.ai-content {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  max-height: 400px;
  overflow-y: auto;
}

.ai-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  line-height: 1.6;
  color: #262626;
}

.single-result,
.discussion-results {
  margin-top: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  width: 100px;
}

:deep(.ant-tabs-content-holder) {
  padding-top: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
