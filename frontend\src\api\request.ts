/**
 * HTTP请求工具模块
 * 基于fetch API的HTTP客户端配置
 */

// 基础配置
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
const DEFAULT_TIMEOUT = 60000  // 增加到60秒，适应AI API调用

// 请求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: any
  timeout?: number
  responseType?: 'json' | 'text' | 'blob' | 'arrayBuffer'
}

// 响应接口
interface ApiResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Headers
}

// 错误类
class ApiError extends Error {
  status: number
  statusText: string
  response?: Response

  constructor(message: string, status: number, statusText: string, response?: Response) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.statusText = statusText
    this.response = response
  }
}

// 超时控制
function createTimeoutSignal(timeout: number): AbortSignal {
  const controller = new AbortController()
  setTimeout(() => controller.abort(), timeout)
  return controller.signal
}

// 请求拦截器
function requestInterceptor(url: string, config: RequestConfig): [string, RequestInit] {
  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`
  
  // 默认headers
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }

  // 合并headers
  const headers = { ...defaultHeaders, ...config.headers }

  // 构建fetch配置
  const fetchConfig: RequestInit = {
    method: config.method || 'GET',
    headers,
    signal: createTimeoutSignal(config.timeout || DEFAULT_TIMEOUT)
  }

  // 处理请求体
  if (config.body && config.method !== 'GET') {
    if (typeof config.body === 'object' && !(config.body instanceof FormData)) {
      fetchConfig.body = JSON.stringify(config.body)
    } else {
      fetchConfig.body = config.body
    }
  }

  return [fullUrl, fetchConfig]
}

// 响应拦截器
async function responseInterceptor<T>(response: Response, responseType: string = 'json'): Promise<ApiResponse<T>> {
  if (!response.ok) {
    let errorMessage = `HTTP Error: ${response.status} ${response.statusText}`
    
    try {
      const errorData = await response.json()
      if (errorData.detail) {
        errorMessage = errorData.detail
      } else if (errorData.message) {
        errorMessage = errorData.message
      }
    } catch {
      // 如果无法解析错误响应，使用默认错误消息
    }
    
    throw new ApiError(errorMessage, response.status, response.statusText, response)
  }

  let data: T
  
  try {
    switch (responseType) {
      case 'json':
        data = await response.json()
        break
      case 'text':
        data = await response.text() as unknown as T
        break
      case 'blob':
        data = await response.blob() as unknown as T
        break
      case 'arrayBuffer':
        data = await response.arrayBuffer() as unknown as T
        break
      default:
        data = await response.json()
    }
  } catch (error) {
    throw new ApiError('Failed to parse response', response.status, response.statusText, response)
  }

  return {
    data,
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  }
}

// 核心请求函数
async function makeRequest<T = any>(url: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
  try {
    const [fullUrl, fetchConfig] = requestInterceptor(url, config)
    const response = await fetch(fullUrl, fetchConfig)
    return await responseInterceptor<T>(response, config.responseType)
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    
    if (error.name === 'AbortError') {
      throw new ApiError('Request timeout', 408, 'Request Timeout')
    }
    
    throw new ApiError(
      error.message || 'Network error',
      0,
      'Network Error'
    )
  }
}

// 导出的请求对象
export const request = {
  // GET请求
  get<T = any>(url: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return makeRequest<T>(url, { ...config, method: 'GET' })
  },

  // POST请求
  post<T = any>(url: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return makeRequest<T>(url, { ...config, method: 'POST', body: data })
  },

  // PUT请求
  put<T = any>(url: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return makeRequest<T>(url, { ...config, method: 'PUT', body: data })
  },

  // DELETE请求
  delete<T = any>(url: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return makeRequest<T>(url, { ...config, method: 'DELETE' })
  },

  // PATCH请求
  patch<T = any>(url: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<ApiResponse<T>> {
    return makeRequest<T>(url, { ...config, method: 'PATCH', body: data })
  }
}

// 导出错误类和类型
export { ApiError }
export type { RequestConfig, ApiResponse }

// 默认导出
export default request
