<template>
  <div class="response-time-chart">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as d3 from 'd3'

interface ChartData {
  time: string
  value: number
}

interface Props {
  data: ChartData[]
}

const props = defineProps<Props>()

const chartContainer = ref<HTMLElement>()
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined>
let xScale: d3.ScalePoint<string>
let yScale: d3.ScaleLinear<number, number>
let line: d3.Line<ChartData>

const margin = { top: 20, right: 30, bottom: 40, left: 50 }
const width = 400 - margin.left - margin.right
const height = 150 - margin.bottom - margin.top

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (svg) {
    svg.remove()
  }
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartContainer.value) return

  // 创建SVG
  svg = d3.select(chartContainer.value)
    .append('svg')
    .attr('width', width + margin.left + margin.right)
    .attr('height', height + margin.top + margin.bottom)

  const g = svg.append('g')
    .attr('transform', `translate(${margin.left},${margin.top})`)

  // 创建比例尺
  xScale = d3.scalePoint()
    .range([0, width])
    .padding(0.1)

  yScale = d3.scaleLinear()
    .range([height, 0])

  // 创建线条生成器
  line = d3.line<ChartData>()
    .x(d => xScale(d.time) || 0)
    .y(d => yScale(d.value))
    .curve(d3.curveMonotoneX)

  // 添加X轴
  g.append('g')
    .attr('class', 'x-axis')
    .attr('transform', `translate(0,${height})`)

  // 添加Y轴
  g.append('g')
    .attr('class', 'y-axis')

  // 添加Y轴标签
  g.append('text')
    .attr('class', 'y-label')
    .attr('transform', 'rotate(-90)')
    .attr('y', 0 - margin.left)
    .attr('x', 0 - (height / 2))
    .attr('dy', '1em')
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#666')
    .text('响应时间 (ms)')

  // 添加线条路径
  g.append('path')
    .attr('class', 'line')
    .style('fill', 'none')
    .style('stroke', '#1890ff')
    .style('stroke-width', 2)

  // 添加数据点
  g.append('g')
    .attr('class', 'dots')

  updateChart()
}

const updateChart = () => {
  if (!svg || !props.data.length) return

  const g = svg.select('g')

  // 更新比例尺域
  xScale.domain(props.data.map(d => d.time))
  yScale.domain([0, d3.max(props.data, d => d.value) || 100])

  // 更新X轴
  g.select('.x-axis')
    .transition()
    .duration(300)
    .call(d3.axisBottom(xScale)
      .tickFormat((d, i) => i % 5 === 0 ? d : '') // 只显示每5个刻度
    )
    .selectAll('text')
    .style('font-size', '10px')
    .style('fill', '#666')

  // 更新Y轴
  g.select('.y-axis')
    .transition()
    .duration(300)
    .call(d3.axisLeft(yScale).ticks(5))
    .selectAll('text')
    .style('font-size', '10px')
    .style('fill', '#666')

  // 更新线条
  g.select('.line')
    .datum(props.data)
    .transition()
    .duration(300)
    .attr('d', line)

  // 更新数据点
  const dots = g.select('.dots')
    .selectAll('.dot')
    .data(props.data)

  dots.enter()
    .append('circle')
    .attr('class', 'dot')
    .attr('r', 3)
    .style('fill', '#1890ff')
    .merge(dots)
    .transition()
    .duration(300)
    .attr('cx', d => xScale(d.time) || 0)
    .attr('cy', d => yScale(d.value))

  dots.exit().remove()

  // 添加悬停效果
  g.select('.dots')
    .selectAll('.dot')
    .on('mouseover', function(event, d) {
      d3.select(this)
        .transition()
        .duration(100)
        .attr('r', 5)
        .style('fill', '#40a9ff')

      // 显示提示框
      const tooltip = d3.select('body')
        .append('div')
        .attr('class', 'chart-tooltip')
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', 'white')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000')
        .html(`时间: ${d.time}<br/>响应时间: ${d.value.toFixed(0)}ms`)

      const [x, y] = d3.pointer(event, document.body)
      tooltip
        .style('left', (x + 10) + 'px')
        .style('top', (y - 10) + 'px')
    })
    .on('mouseout', function() {
      d3.select(this)
        .transition()
        .duration(100)
        .attr('r', 3)
        .style('fill', '#1890ff')

      d3.selectAll('.chart-tooltip').remove()
    })
}
</script>

<style scoped>
.response-time-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}

:deep(.x-axis) .domain,
:deep(.y-axis) .domain {
  stroke: #d9d9d9;
}

:deep(.x-axis) .tick line,
:deep(.y-axis) .tick line {
  stroke: #d9d9d9;
}
</style>
