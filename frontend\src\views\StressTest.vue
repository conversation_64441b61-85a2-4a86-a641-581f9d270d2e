<template>
  <div class="stress-test">
    <a-page-header
      title="性能压力测试"
      sub-title="测试AI叙事引擎在高负载下的性能表现和稳定性"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button 
            @click="startStressTest" 
            :loading="testRunning" 
            type="primary"
            :disabled="testRunning"
          >
            <template #icon><ThunderboltOutlined /></template>
            开始压力测试
          </a-button>
          <a-button @click="stopStressTest" :disabled="!testRunning" danger>
            <template #icon><StopOutlined /></template>
            停止测试
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="stress-content">
      <!-- 测试配置 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="测试配置" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="并发用户数">
                  <a-input-number
                    v-model:value="testConfig.concurrentUsers"
                    :min="1"
                    :max="100"
                    :disabled="testRunning"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测试时长(秒)">
                  <a-input-number
                    v-model:value="testConfig.duration"
                    :min="10"
                    :max="300"
                    :disabled="testRunning"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="请求间隔(ms)">
                  <a-input-number
                    v-model:value="testConfig.requestInterval"
                    :min="100"
                    :max="5000"
                    :disabled="testRunning"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="测试类型">
                  <a-select
                    v-model:value="testConfig.testType"
                    :disabled="testRunning"
                  >
                    <a-select-option value="api">API压力测试</a-select-option>
                    <a-select-option value="memory">记忆系统测试</a-select-option>
                    <a-select-option value="relationship">关系网络测试</a-select-option>
                    <a-select-option value="mixed">混合负载测试</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时监控 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="实时监控" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="测试状态"
                  :value="testStatus"
                  :value-style="{ color: getStatusColor() }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="已运行时间"
                  :value="elapsedTime"
                  suffix="秒"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="剩余时间"
                  :value="remainingTime"
                  suffix="秒"
                  :value-style="{ color: '#faad14' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="测试进度"
                  :value="testProgress"
                  suffix="%"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
            </a-row>

            <a-divider size="small" />

            <a-progress
              :percent="testProgress"
              :stroke-color="getProgressColor()"
              :status="testRunning ? 'active' : 'normal'"
            />
          </a-card>
        </a-col>
      </a-row>

      <!-- 性能指标 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="12">
          <a-card title="请求统计" size="small">
            <a-row :gutter="8">
              <a-col :span="12">
                <a-statistic
                  title="总请求数"
                  :value="metrics.totalRequests"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="成功请求"
                  :value="metrics.successfulRequests"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="失败请求"
                  :value="metrics.failedRequests"
                  :value-style="{ color: '#f5222d' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="成功率"
                  :value="successRate"
                  :precision="1"
                  suffix="%"
                  :value-style="{ color: getSuccessRateColor() }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="性能指标" size="small">
            <a-row :gutter="8">
              <a-col :span="12">
                <a-statistic
                  title="平均响应时间"
                  :value="metrics.averageResponseTime"
                  :precision="0"
                  suffix="ms"
                  :value-style="{ color: getResponseTimeColor() }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="最大响应时间"
                  :value="metrics.maxResponseTime"
                  :precision="0"
                  suffix="ms"
                  :value-style="{ color: '#faad14' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="QPS"
                  :value="metrics.qps"
                  :precision="1"
                  suffix="req/s"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-col>
              <a-col :span="12">
                <a-statistic
                  title="并发数"
                  :value="metrics.currentConcurrency"
                  :value-style="{ color: '#13c2c2' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时图表 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="12">
          <a-card title="响应时间趋势" size="small">
            <div class="chart-container">
              <ResponseTimeChart :data="responseTimeData" />
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="QPS趋势" size="small">
            <div class="chart-container">
              <QpsChart :data="qpsData" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 错误日志 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="错误日志" size="small">
            <template #extra>
              <a-space>
                <a-badge :count="errorLogs.length" :number-style="{ backgroundColor: '#f5222d' }">
                  <span>错误数量</span>
                </a-badge>
                <a-button size="small" @click="clearErrorLogs">
                  <template #icon><DeleteOutlined /></template>
                  清空
                </a-button>
              </a-space>
            </template>

            <div class="error-logs">
              <div
                v-for="(error, index) in errorLogs"
                :key="index"
                class="error-entry"
              >
                <span class="error-time">{{ formatTime(error.timestamp) }}</span>
                <span class="error-type">{{ error.type }}</span>
                <span class="error-message">{{ error.message }}</span>
              </div>

              <a-empty v-if="errorLogs.length === 0" description="暂无错误" size="small" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ThunderboltOutlined,
  StopOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import ResponseTimeChart from '@/components/charts/ResponseTimeChart.vue'
import QpsChart from '@/components/charts/QpsChart.vue'
import { request } from '@/api/request'

// 测试配置
const testConfig = reactive({
  concurrentUsers: 10,
  duration: 60,
  requestInterval: 1000,
  testType: 'mixed'
})

// 测试状态
const testRunning = ref(false)
const testStatus = ref('待开始')
const startTime = ref<Date | null>(null)
const elapsedTime = ref(0)
const testTimer = ref<number | null>(null)
const metricsTimer = ref<number | null>(null)

// 性能指标
const metrics = reactive({
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  averageResponseTime: 0,
  maxResponseTime: 0,
  qps: 0,
  currentConcurrency: 0
})

// 图表数据
const responseTimeData = ref<Array<{ time: string, value: number }>>([])
const qpsData = ref<Array<{ time: string, value: number }>>([])

// 错误日志
const errorLogs = ref<Array<{
  timestamp: Date
  type: string
  message: string
}>>([])

// 活跃请求
const activeRequests = ref(new Set<string>())

// 计算属性
const remainingTime = computed(() => {
  if (!testRunning.value || !startTime.value) return 0
  return Math.max(0, testConfig.duration - elapsedTime.value)
})

const testProgress = computed(() => {
  if (!testRunning.value || testConfig.duration === 0) return 0
  return Math.min(100, (elapsedTime.value / testConfig.duration) * 100)
})

const successRate = computed(() => {
  if (metrics.totalRequests === 0) return 0
  return (metrics.successfulRequests / metrics.totalRequests) * 100
})

// 生命周期
onMounted(() => {
  // 初始化图表数据
  updateChartData()
})

onUnmounted(() => {
  stopStressTest()
})

// 方法
const startStressTest = async () => {
  if (testRunning.value) return

  testRunning.value = true
  testStatus.value = '运行中'
  startTime.value = new Date()
  elapsedTime.value = 0

  // 重置指标
  Object.assign(metrics, {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    maxResponseTime: 0,
    qps: 0,
    currentConcurrency: 0
  })

  // 清空数据
  responseTimeData.value = []
  qpsData.value = []
  errorLogs.value = []
  activeRequests.value.clear()

  message.info('压力测试开始')

  // 启动计时器
  testTimer.value = window.setInterval(() => {
    elapsedTime.value++
    
    if (elapsedTime.value >= testConfig.duration) {
      stopStressTest()
    }
  }, 1000)

  // 启动指标更新
  metricsTimer.value = window.setInterval(() => {
    updateMetrics()
    updateChartData()
  }, 1000)

  // 启动并发请求
  for (let i = 0; i < testConfig.concurrentUsers; i++) {
    startUserSimulation(i)
  }
}

const stopStressTest = () => {
  if (!testRunning.value) return

  testRunning.value = false
  testStatus.value = '已停止'

  // 清理计时器
  if (testTimer.value) {
    clearInterval(testTimer.value)
    testTimer.value = null
  }

  if (metricsTimer.value) {
    clearInterval(metricsTimer.value)
    metricsTimer.value = null
  }

  message.success('压力测试已停止')
}

const startUserSimulation = async (userId: number) => {
  while (testRunning.value) {
    const requestId = `user-${userId}-${Date.now()}`
    activeRequests.value.add(requestId)
    
    try {
      await simulateRequest(requestId)
    } catch (error) {
      // 错误已在simulateRequest中处理
    }
    
    activeRequests.value.delete(requestId)
    
    // 等待下次请求
    await new Promise(resolve => setTimeout(resolve, testConfig.requestInterval))
  }
}

const simulateRequest = async (requestId: string) => {
  const startTime = Date.now()
  
  try {
    metrics.totalRequests++
    
    // 根据测试类型选择API
    let apiCall: Promise<any>
    
    switch (testConfig.testType) {
      case 'api':
        apiCall = request.get('/api/health')
        break
      case 'memory':
        apiCall = request.get('/api/memory/statistics/director')
        break
      case 'relationship':
        apiCall = request.get('/api/memory/relationships/director')
        break
      case 'mixed':
      default:
        const apis = [
          '/api/health',
          '/api/performance/metrics',
          '/api/memory/statistics/director',
          '/api/memory/relationships/director'
        ]
        const randomApi = apis[Math.floor(Math.random() * apis.length)]
        apiCall = request.get(randomApi)
        break
    }
    
    await apiCall
    
    const responseTime = Date.now() - startTime
    metrics.successfulRequests++
    
    // 更新响应时间统计
    updateResponseTimeStats(responseTime)
    
  } catch (error) {
    metrics.failedRequests++
    
    // 记录错误
    errorLogs.value.unshift({
      timestamp: new Date(),
      type: 'REQUEST_ERROR',
      message: error.message || '请求失败'
    })
    
    // 限制错误日志数量
    if (errorLogs.value.length > 100) {
      errorLogs.value = errorLogs.value.slice(0, 100)
    }
  }
}

const updateResponseTimeStats = (responseTime: number) => {
  // 更新平均响应时间
  const totalResponseTime = metrics.averageResponseTime * (metrics.successfulRequests - 1) + responseTime
  metrics.averageResponseTime = totalResponseTime / metrics.successfulRequests
  
  // 更新最大响应时间
  metrics.maxResponseTime = Math.max(metrics.maxResponseTime, responseTime)
}

const updateMetrics = () => {
  // 更新并发数
  metrics.currentConcurrency = activeRequests.value.size
  
  // 计算QPS
  if (elapsedTime.value > 0) {
    metrics.qps = metrics.totalRequests / elapsedTime.value
  }
}

const updateChartData = () => {
  const now = new Date().toLocaleTimeString('zh-CN')
  
  // 更新响应时间图表
  responseTimeData.value.push({
    time: now,
    value: metrics.averageResponseTime
  })
  
  // 更新QPS图表
  qpsData.value.push({
    time: now,
    value: metrics.qps
  })
  
  // 限制数据点数量
  if (responseTimeData.value.length > 60) {
    responseTimeData.value = responseTimeData.value.slice(-60)
  }
  
  if (qpsData.value.length > 60) {
    qpsData.value = qpsData.value.slice(-60)
  }
}

const clearErrorLogs = () => {
  errorLogs.value = []
  message.info('错误日志已清空')
}

// 辅助函数
const getStatusColor = () => {
  if (testStatus.value === '运行中') return '#52c41a'
  if (testStatus.value === '已停止') return '#faad14'
  return '#666'
}

const getProgressColor = () => {
  if (testProgress.value < 30) return '#52c41a'
  if (testProgress.value < 70) return '#faad14'
  return '#f5222d'
}

const getSuccessRateColor = () => {
  if (successRate.value >= 95) return '#52c41a'
  if (successRate.value >= 80) return '#faad14'
  return '#f5222d'
}

const getResponseTimeColor = () => {
  if (metrics.averageResponseTime < 500) return '#52c41a'
  if (metrics.averageResponseTime < 1000) return '#faad14'
  return '#f5222d'
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN')
}
</script>

<style scoped>
.stress-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.stress-content {
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.chart-container {
  height: 200px;
  padding: 8px;
}

.error-logs {
  height: 200px;
  overflow-y: auto;
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.error-entry {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 2px 0;
}

.error-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.error-type {
  color: #f5222d;
  margin-right: 8px;
  min-width: 120px;
  font-weight: bold;
}

.error-message {
  flex: 1;
  color: #262626;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-form-item-label) {
  font-size: 12px;
}
</style>
