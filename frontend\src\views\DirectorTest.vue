<template>
  <div class="director-test">
    <a-page-header
      title="@导演功能测试"
      sub-title="使用HTTP API直接测试AI导演响应"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag color="blue">HTTP API测试</a-tag>
          <a-button @click="testSiliconFlowConnection" :loading="testingConnection">
            <template #icon><ApiOutlined /></template>
            测试API
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <a-row :gutter="16">
        <!-- 左侧：测试控制 -->
        <a-col :span="8">
          <a-card title="导演AI测试" size="small">
            <a-form layout="vertical">
              <a-form-item label="测试类型">
                <a-select v-model:value="testType" @change="onTestTypeChange">
                  <a-select-option value="creative">创意构思</a-select-option>
                  <a-select-option value="plot">剧情规划</a-select-option>
                  <a-select-option value="discussion">多角色讨论</a-select-option>
                  <a-select-option value="custom">自定义指令</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="测试内容">
                <a-textarea
                  v-model:value="testContent"
                  :placeholder="getPlaceholder()"
                  :rows="4"
                />
              </a-form-item>

              <a-form-item>
                <a-space direction="vertical" style="width: 100%">
                  <a-button 
                    type="primary" 
                    block
                    @click="testDirector"
                    :disabled="!testContent.trim()"
                    :loading="testing"
                  >
                    <template #icon><RobotOutlined /></template>
                    测试@导演
                  </a-button>
                  
                  <a-button block @click="clearResults">
                    <template #icon><ClearOutlined /></template>
                    清空结果
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>

            <a-divider>快速测试</a-divider>
            
            <a-space direction="vertical" style="width: 100%">
              <a-button block @click="quickTest('creative', '设计一个关于时间旅行的科幻故事')">
                创意构思测试
              </a-button>
              <a-button block @click="quickTest('plot', '主角发现自己可以穿越时空，但每次穿越都会改变历史')">
                剧情规划测试
              </a-button>
              <a-button block @click="quickTest('discussion', '如何让时间旅行故事更有吸引力')">
                多角色讨论测试
              </a-button>
            </a-space>
          </a-card>
        </a-col>

        <!-- 右侧：测试结果 -->
        <a-col :span="16">
          <a-card title="AI导演响应" size="small">
            <template #extra>
              <a-space>
                <a-tag v-if="lastResponseTime" color="green">
                  响应时间: {{ lastResponseTime }}ms
                </a-tag>
                <a-tag v-if="apiStatus" :color="apiStatus === 'success' ? 'green' : 'red'">
                  {{ apiStatus === 'success' ? 'API正常' : 'API异常' }}
                </a-tag>
              </a-space>
            </template>

            <div v-if="testing" class="testing-indicator">
              <a-spin size="large">
                <template #indicator>
                  <LoadingOutlined style="font-size: 24px" spin />
                </template>
              </a-spin>
              <p>AI导演正在思考中，请稍候...</p>
            </div>

            <div v-else-if="testResults.length > 0" class="results-container">
              <div 
                v-for="(result, index) in testResults" 
                :key="index"
                class="result-item"
              >
                <div class="result-header">
                  <a-avatar style="background-color: #1890ff">
                    🎬
                  </a-avatar>
                  <span class="result-title">AI导演 - {{ result.type }}</span>
                  <span class="result-time">{{ formatTime(result.timestamp) }}</span>
                </div>

                <div class="result-content">
                  <div class="user-input">
                    <strong>用户指令：</strong>{{ result.input }}
                  </div>
                  
                  <a-divider />
                  
                  <div v-if="result.success" class="ai-response">
                    <strong>AI导演响应：</strong>
                    <div class="response-content">
                      {{ result.response }}
                    </div>
                  </div>
                  
                  <div v-else class="error-response">
                    <a-alert
                      :message="result.error"
                      type="error"
                      show-icon
                    />
                  </div>
                </div>

                <div class="result-meta">
                  <a-descriptions :column="3" size="small">
                    <a-descriptions-item label="模型">
                      {{ result.model || 'Unknown' }}
                    </a-descriptions-item>
                    <a-descriptions-item label="响应时间">
                      {{ result.responseTime }}ms
                    </a-descriptions-item>
                    <a-descriptions-item label="状态">
                      <a-tag :color="result.success ? 'green' : 'red'">
                        {{ result.success ? '成功' : '失败' }}
                      </a-tag>
                    </a-descriptions-item>
                  </a-descriptions>
                </div>
              </div>
            </div>

            <a-empty v-else description="点击测试按钮开始体验AI导演功能" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 测试统计 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="测试统计" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总测试次数"
                  :value="testResults.length"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="成功次数"
                  :value="successCount"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="平均响应时间"
                  :value="averageResponseTime"
                  suffix="ms"
                  :precision="0"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="成功率"
                  :value="successRate"
                  suffix="%"
                  :precision="1"
                  :value-style="{ color: successRate > 80 ? '#52c41a' : '#f5222d' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  ApiOutlined,
  RobotOutlined,
  ClearOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import { siliconflowApi } from '@/api/siliconflow'

// 响应式数据
const testType = ref('creative')
const testContent = ref('')
const testing = ref(false)
const testingConnection = ref(false)
const testResults = ref<any[]>([])
const lastResponseTime = ref(0)
const apiStatus = ref('')

// 计算属性
const successCount = computed(() => 
  testResults.value.filter(result => result.success).length
)

const averageResponseTime = computed(() => {
  if (testResults.value.length === 0) return 0
  const total = testResults.value.reduce((sum, result) => sum + result.responseTime, 0)
  return total / testResults.value.length
})

const successRate = computed(() => {
  if (testResults.value.length === 0) return 0
  return (successCount.value / testResults.value.length) * 100
})

// 方法
const getPlaceholder = () => {
  const placeholders = {
    creative: '请描述你想要的创意主题，例如：一个关于AI觉醒的科幻故事...',
    plot: '请提供故事大纲，例如：主角发现了一个改变世界的秘密...',
    discussion: '请提供讨论主题，例如：如何让故事更有吸引力...',
    custom: '请输入你想对AI导演说的话...'
  }
  return placeholders[testType.value] || '请输入测试内容...'
}

const onTestTypeChange = () => {
  testContent.value = ''
}

const testSiliconFlowConnection = async () => {
  testingConnection.value = true
  try {
    const response = await siliconflowApi.testConnection()
    if (response.data.success) {
      apiStatus.value = 'success'
      message.success('Silicon Flow API连接正常')
    } else {
      apiStatus.value = 'error'
      message.error('Silicon Flow API连接失败')
    }
  } catch (error) {
    apiStatus.value = 'error'
    message.error('API测试失败: ' + error.message)
  } finally {
    testingConnection.value = false
  }
}

const testDirector = async () => {
  if (!testContent.value.trim()) return
  
  testing.value = true
  const startTime = Date.now()
  
  try {
    let response
    
    switch (testType.value) {
      case 'creative':
        response = await siliconflowApi.generateCreativeIdea({
          topic: testContent.value,
          context: '这是一个AI导演功能测试'
        })
        break
        
      case 'plot':
        response = await siliconflowApi.planPlot({
          story_outline: testContent.value,
          requirements: '请提供专业的导演视角分析'
        })
        break
        
      case 'discussion':
        response = await siliconflowApi.multiAgentDiscussion({
          topic: testContent.value,
          participants: ['director'],
          context: '单独测试导演AI的响应'
        })
        break
        
      case 'custom':
        response = await siliconflowApi.generateText({
          content_type: '导演指导',
          requirements: testContent.value,
          style: '专业导演风格'
        })
        break
        
      default:
        throw new Error('未知的测试类型')
    }
    
    const responseTime = Date.now() - startTime
    lastResponseTime.value = responseTime
    
    let aiResponse = ''
    let model = ''
    let success = false
    
    if (response.data.success) {
      success = true
      if (testType.value === 'discussion') {
        // 多角色讨论的响应格式不同
        const directorResult = response.data.data.results?.director
        if (directorResult?.success) {
          aiResponse = directorResult.data?.choices?.[0]?.message?.content || '无响应内容'
          model = directorResult.data?.model || 'Unknown'
        } else {
          success = false
          aiResponse = directorResult?.error || '响应失败'
        }
      } else {
        // 单一响应格式
        aiResponse = response.data.data?.choices?.[0]?.message?.content || '无响应内容'
        model = response.data.data?.model || 'Unknown'
      }
    } else {
      aiResponse = response.data.error || '未知错误'
    }
    
    // 添加测试结果
    testResults.value.unshift({
      type: getTestTypeName(testType.value),
      input: testContent.value,
      response: aiResponse,
      model: model,
      responseTime: responseTime,
      success: success,
      error: success ? null : aiResponse,
      timestamp: Date.now()
    })
    
    if (success) {
      message.success('AI导演响应成功！')
    } else {
      message.error('AI导演响应失败')
    }
    
  } catch (error) {
    const responseTime = Date.now() - startTime
    lastResponseTime.value = responseTime
    
    testResults.value.unshift({
      type: getTestTypeName(testType.value),
      input: testContent.value,
      response: '',
      model: 'Unknown',
      responseTime: responseTime,
      success: false,
      error: error.message,
      timestamp: Date.now()
    })
    
    message.error('测试失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const quickTest = async (type: string, content: string) => {
  testType.value = type
  testContent.value = content
  await testDirector()
}

const clearResults = () => {
  testResults.value = []
  lastResponseTime.value = 0
  message.success('测试结果已清空')
}

const getTestTypeName = (type: string) => {
  const names = {
    creative: '创意构思',
    plot: '剧情规划',
    discussion: '多角色讨论',
    custom: '自定义指令'
  }
  return names[type] || type
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
.director-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.testing-indicator {
  text-align: center;
  padding: 60px;
}

.testing-indicator p {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

.results-container {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.result-title {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
}

.result-time {
  margin-left: auto;
  color: #999;
  font-size: 12px;
}

.user-input {
  padding: 12px;
  background: #f0f2f5;
  border-radius: 6px;
  margin-bottom: 8px;
}

.ai-response {
  padding: 12px;
  background: #f6ffed;
  border-radius: 6px;
  border-left: 3px solid #52c41a;
}

.response-content {
  margin-top: 8px;
  line-height: 1.6;
  white-space: pre-wrap;
  color: #262626;
}

.error-response {
  margin-top: 8px;
}

.result-meta {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
