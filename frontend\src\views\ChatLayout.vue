<template>
  <a-layout class="chat-layout">
    <!-- 顶部导航栏 -->
    <a-layout-header class="chat-header">
      <div class="header-content">
        <div class="header-left">
          <h1>群聊式AI叙事引擎</h1>
          <a-menu mode="horizontal" :selected-keys="[currentRoute]" class="nav-menu">
            <a-menu-item key="chat" @click="navigateTo('/')">
              <template #icon><a-icon type="message" /></template>
              聊天模式
            </a-menu-item>
            <a-menu-item key="collaboration" @click="navigateTo('/collaboration')">
              <template #icon><a-icon type="team" /></template>
              协作工作台
            </a-menu-item>
            <a-menu-item key="memory" @click="navigateTo('/memory')">
              <template #icon><a-icon type="brain" /></template>
              记忆管理
            </a-menu-item>
            <a-menu-item key="relationship" @click="navigateTo('/relationship')">
              <template #icon><a-icon type="share-alt" /></template>
              关系网络
            </a-menu-item>
            <a-menu-item key="performance" @click="navigateTo('/performance')">
              <template #icon><a-icon type="dashboard" /></template>
              性能监控
            </a-menu-item>
            <a-menu-item key="personality" @click="navigateTo('/personality')">
              <template #icon><a-icon type="user" /></template>
              个性设置
            </a-menu-item>
            <a-menu-item key="test" @click="navigateTo('/test')">
              <template #icon><a-icon type="experiment" /></template>
              系统测试
            </a-menu-item>
            <a-menu-item key="stress" @click="navigateTo('/stress')">
              <template #icon><a-icon type="thunderbolt" /></template>
              压力测试
            </a-menu-item>
            <a-menu-item key="ai-collaboration" @click="navigateTo('/ai-collaboration')">
              <template #icon><a-icon type="robot" /></template>
              AI协作
            </a-menu-item>
            <a-menu-item key="agent-test" @click="navigateTo('/agent-test')">
              <template #icon><a-icon type="experiment" /></template>
              角色测试
            </a-menu-item>
            <a-menu-item key="socket-test" @click="navigateTo('/socket-test')">
              <template #icon><a-icon type="api" /></template>
              Socket测试
            </a-menu-item>
            <a-menu-item key="director-test" @click="navigateTo('/director-test')">
              <template #icon><a-icon type="video-camera" /></template>
              导演测试
            </a-menu-item>
            <a-menu-item key="mention-debug" @click="navigateTo('/mention-debug')">
              <template #icon><a-icon type="bug" /></template>
              @功能调试
            </a-menu-item>
            <a-menu-item key="timeout-test" @click="navigateTo('/timeout-test')">
              <template #icon><a-icon type="clock-circle" /></template>
              超时测试
            </a-menu-item>
            <a-menu-item key="chat-test" @click="navigateTo('/chat-test')">
              <template #icon><a-icon type="message" /></template>
              聊天测试
            </a-menu-item>
          </a-menu>
        </div>
        <a-space>
          <a-tag :color="connectionStatus === 'connected' ? 'green' : connectionStatus === 'error' ? 'red' : 'blue'" size="small">
            Socket: {{ connectionStatus === 'connected' ? '已连接' : connectionStatus === 'error' ? '连接失败' : '连接中' }}
          </a-tag>
          <a-tag color="green" size="small">
            @导演功能: 可用
          </a-tag>
        </a-space>
      </div>
    </a-layout-header>

    <!-- 主体内容区域 -->
    <a-layout class="chat-container">
      <!-- 左侧：Agent状态面板 -->
      <a-layout-sider width="280" theme="light" class="agent-panel">
        <AgentCards />
      </a-layout-sider>

      <!-- 中间：聊天区域 -->
      <a-layout-content class="chat-area">
        <div class="message-container">
          <div class="message-list" ref="messageList">
            <div
              v-for="message in messages"
              :key="message.id"
              :class="['message-item', message.type]"
            >
              <a-card
                size="small"
                :class="['message-card', message.type]"
                :body-style="{ padding: '12px' }"
              >
                <template #title>
                  <div class="message-header">
                    <a-avatar size="small" :style="{ backgroundColor: getAvatarColor(message.type) }">
                      {{ getAvatarText(message.sender) }}
                    </a-avatar>
                    <span class="sender">{{ message.sender }}</span>
                    <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
                  </div>
                </template>
                <div class="message-content">{{ message.content }}</div>
              </a-card>
            </div>
          </div>

          <div class="input-area">
            <MentionInput
              v-model="currentMessage"
              @send="handleSendMessage"
              :disabled="false"
            />
          </div>
        </div>
      </a-layout-content>

      <!-- 右侧：工作流状态 -->
      <a-layout-sider width="280" theme="light" class="workflow-panel">
        <WorkflowStatus />
      </a-layout-sider>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useChatStore } from '@/stores/chatStore'
import { useSocketStore } from '@/stores/socketStore'
import AgentCards from '@/components/AgentCards.vue'
import MentionInput from '@/components/MentionInput.vue'
import WorkflowStatus from '@/components/WorkflowStatus.vue'

const router = useRouter()
const route = useRoute()
const chatStore = useChatStore()
const socketStore = useSocketStore()

const currentMessage = ref('')
const messageList = ref<HTMLElement>()

const messages = computed(() => chatStore.messages)
const connectionStatus = computed(() => socketStore.connectionStatus)
const currentRoute = computed(() => {
  if (route.path === '/collaboration') return 'collaboration'
  if (route.path === '/memory') return 'memory'
  if (route.path === '/relationship') return 'relationship'
  if (route.path === '/performance') return 'performance'
  if (route.path === '/personality') return 'personality'
  if (route.path === '/test') return 'test'
  if (route.path === '/stress') return 'stress'
  if (route.path === '/ai-collaboration') return 'ai-collaboration'
  if (route.path === '/agent-test') return 'agent-test'
  if (route.path === '/socket-test') return 'socket-test'
  if (route.path === '/director-test') return 'director-test'
  if (route.path === '/mention-debug') return 'mention-debug'
  if (route.path === '/timeout-test') return 'timeout-test'
  if (route.path === '/chat-test') return 'chat-test'
  return 'chat'
})

const handleSendMessage = (message: string) => {
  if (message.trim()) {
    chatStore.sendMessage(message)
    currentMessage.value = ''
    scrollToBottom()
  }
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getAvatarColor = (type: string) => {
  const colorMap: Record<string, string> = {
    user: '#1890ff',
    agent: '#52c41a',
    system: '#fa8c16'
  }
  return colorMap[type] || '#d9d9d9'
}

const getAvatarText = (sender: string) => {
  if (sender === '用户') return '我'
  if (sender === '系统') return '系'
  return sender.charAt(0)
}

const navigateTo = (path: string) => {
  router.push(path)
}

const scrollToBottom = async () => {
  await nextTick()
  if (messageList.value) {
    messageList.value.scrollTop = messageList.value.scrollHeight
  }
}

onMounted(() => {
  socketStore.connect()
})
</script>

<style scoped>
.chat-layout {
  height: 100vh;
}

.chat-header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-content h1 {
  margin: 0;
  font-size: 20px;
  color: #1890ff;
  font-weight: 600;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
}

.nav-menu :deep(.ant-menu-item) {
  border-bottom: 2px solid transparent;
}

.nav-menu :deep(.ant-menu-item-selected) {
  border-bottom-color: #1890ff;
}

.status-text {
  font-size: 14px;
  color: #666;
}

.chat-container {
  height: calc(100vh - 64px);
}

.agent-panel,
.workflow-panel {
  background: white;
  border-right: 1px solid #f0f0f0;
}

.chat-area {
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.message-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.message-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #fafafa;
}

.message-item {
  display: flex;
}

.message-item.user {
  justify-content: flex-end;
}

.message-item.agent {
  justify-content: flex-start;
}

.message-item.system {
  justify-content: center;
}

.message-card {
  max-width: 70%;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.message-card.user {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.message-card.agent {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.message-card.system {
  background: #fff7e6;
  border-color: #ffd591;
  max-width: 50%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.sender {
  font-weight: 500;
  color: #262626;
}

.timestamp {
  color: #8c8c8c;
  margin-left: auto;
}

.message-content {
  line-height: 1.6;
  color: #262626;
  margin-top: 4px;
}

.input-area {
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  background: white;
}

/* 滚动条样式 */
.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
