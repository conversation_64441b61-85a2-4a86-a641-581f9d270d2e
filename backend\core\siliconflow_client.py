"""
Silicon Flow API客户端
集成Silicon Flow API到AutoGen系统中
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Any
from datetime import datetime

# 导入配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai import SILICONFLOW_API_KEY, SILICONFLOW_BASE_URL

logger = logging.getLogger(__name__)

class SiliconFlowClient:
    """Silicon Flow API客户端"""
    
    def __init__(self, api_key: str = SILICONFLOW_API_KEY, base_url: str = SILICONFLOW_BASE_URL):
        self.api_key = api_key
        self.base_url = base_url
        self.session = None
        
        # 角色专用模型配置
        self.role_models = {
            "director": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",  # 导演使用推理模型
            "designer": "Qwen/Qwen2.5-72B-Instruct",  # 设计师使用大模型
            "writer": "deepseek-ai/deepseek-chat"  # 撰写师使用对话模型
        }
        
        # 角色系统提示词
        self.role_prompts = {
            "director": """你是一位经验丰富的电影导演，负责整体创意构思和项目协调。
你的特点：
- 具有敏锐的艺术直觉和商业嗅觉
- 善于统筹规划和团队协调
- 注重故事的整体结构和视觉呈现
- 能够平衡创意与实用性

请以专业导演的身份参与讨论，提供有价值的创意指导。""",

            "designer": """你是一位才华横溢的视觉设计师，负责角色设计和视觉概念。
你的特点：
- 具有出色的视觉想象力和美学素养
- 精通角色造型、场景设计和色彩搭配
- 能够将抽象概念转化为具体的视觉元素
- 注重细节和整体风格的统一

请以专业设计师的身份参与讨论，提供创新的视觉设计方案。""",

            "writer": """你是一位优秀的编剧和文案撰写师，负责剧情规划和文本创作。
你的特点：
- 具有深厚的文学功底和叙事技巧
- 善于构建引人入胜的故事情节
- 精通对话写作和人物塑造
- 能够适应不同的文体和风格要求

请以专业编剧的身份参与讨论，提供精彩的故事内容和文本创作。"""
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        return False
    
    async def chat_completion(self,
                            messages: List[Dict[str, str]],
                            role: str = "director",
                            temperature: float = 0.7,
                            max_tokens: int = 1000,
                            stream: bool = False,
                            max_retries: int = 3,
                            retry_delay: float = 2.0) -> Dict[str, Any]:
        """
        调用Silicon Flow聊天完成API - 增强版本，支持重试和备用模型

        Args:
            messages: 消息列表
            role: 角色类型 (director/designer/writer)
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式输出
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）

        Returns:
            API响应结果
        """
        if not self.session:
            self.session = aiohttp.ClientSession()

        # 获取角色对应的模型列表（主模型 + 备用模型）
        model_options = self._get_model_options(role)

        # 添加角色系统提示词
        system_prompt = self.role_prompts.get(role, "")
        if system_prompt and (not messages or messages[0].get("role") != "system"):
            messages = [{"role": "system", "content": system_prompt}] + messages

        url = f"{self.base_url}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 尝试每个模型
        for model_index, model in enumerate(model_options):
            logger.info(f"尝试模型 {model_index + 1}/{len(model_options)}: {model}")

            # 对每个模型进行重试
            for attempt in range(max_retries):
                try:
                    payload = {
                        "model": model,
                        "messages": messages,
                        "temperature": temperature,
                        "max_tokens": max_tokens,
                        "stream": stream
                    }

                    logger.info(f"第 {attempt + 1}/{max_retries} 次尝试调用 {model}")

                    async with self.session.post(url, json=payload, headers=headers, timeout=60) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"✅ 模型 {model} 调用成功")
                            return {
                                "success": True,
                                "data": result,
                                "role": role,
                                "model": model,
                                "attempt": attempt + 1,
                                "timestamp": datetime.now().isoformat()
                            }
                        elif response.status == 503:
                            # 503错误，服务器繁忙，等待后重试
                            error_text = await response.text()
                            logger.warning(f"服务器繁忙 (503)，第 {attempt + 1} 次尝试失败: {error_text}")

                            if attempt < max_retries - 1:
                                wait_time = retry_delay * (2 ** attempt)  # 指数退避
                                logger.info(f"等待 {wait_time} 秒后重试...")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                logger.warning(f"模型 {model} 重试次数已用完，尝试下一个模型")
                                break
                        else:
                            # 其他错误，直接尝试下一个模型
                            error_text = await response.text()
                            logger.error(f"API错误 {response.status}: {error_text}")
                            break

                except asyncio.TimeoutError:
                    logger.warning(f"请求超时，第 {attempt + 1} 次尝试失败")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        break
                except Exception as e:
                    logger.error(f"请求异常: {str(e)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        break

        # 所有模型和重试都失败了
        logger.error("所有模型调用都失败了")
        return {
            "success": False,
            "error": "所有模型都暂时不可用，请稍后重试",
            "role": role,
            "model": "all_failed",
            "timestamp": datetime.now().isoformat()
        }

    def _get_model_options(self, role: str) -> List[str]:
        """获取角色对应的模型选项（主模型 + 备用模型）"""
        # 主模型
        primary_model = self.role_models.get(role, self.role_models["director"])

        # 备用模型列表
        backup_models = {
            "director": [
                "deepseek-ai/deepseek-chat",  # 备用1：deepseek-chat
                "Qwen/Qwen2.5-7B-Instruct"   # 备用2：较小的Qwen模型
            ],
            "designer": [
                "deepseek-ai/deepseek-chat",  # 备用1：deepseek-chat
                "Qwen/Qwen2.5-7B-Instruct"   # 备用2：较小的Qwen模型
            ],
            "writer": [
                "Qwen/Qwen2.5-7B-Instruct",  # 备用1：Qwen模型
                "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"  # 备用2：推理模型
            ]
        }

        # 组合主模型和备用模型
        models = [primary_model]
        if role in backup_models:
            models.extend(backup_models[role])

        # 去重
        return list(dict.fromkeys(models))
    
    async def generate_creative_idea(self, topic: str, context: str = "") -> Dict[str, Any]:
        """
        生成创意构思
        
        Args:
            topic: 主题
            context: 上下文信息
            
        Returns:
            创意构思结果
        """
        messages = [
            {
                "role": "user",
                "content": f"""请为以下主题生成创意构思：

主题：{topic}
上下文：{context}

请提供：
1. 核心创意概念
2. 目标受众分析
3. 独特卖点
4. 可行性评估
5. 发展方向建议

请以专业导演的视角，提供具有商业价值和艺术价值的创意方案。"""
            }
        ]
        
        return await self.chat_completion(messages, role="director", temperature=0.8)
    
    async def design_character(self, character_info: str, style_requirements: str = "") -> Dict[str, Any]:
        """
        设计角色
        
        Args:
            character_info: 角色信息
            style_requirements: 风格要求
            
        Returns:
            角色设计结果
        """
        messages = [
            {
                "role": "user",
                "content": f"""请设计以下角色：

角色信息：{character_info}
风格要求：{style_requirements}

请提供：
1. 外观设计描述
2. 性格特征分析
3. 服装造型设计
4. 色彩搭配方案
5. 设计理念说明

请以专业设计师的视角，创造独特且符合需求的角色设计。"""
            }
        ]
        
        return await self.chat_completion(messages, role="designer", temperature=0.9)
    
    async def plan_plot(self, story_outline: str, requirements: str = "") -> Dict[str, Any]:
        """
        规划剧情
        
        Args:
            story_outline: 故事大纲
            requirements: 具体要求
            
        Returns:
            剧情规划结果
        """
        messages = [
            {
                "role": "user",
                "content": f"""请规划以下故事的剧情：

故事大纲：{story_outline}
具体要求：{requirements}

请提供：
1. 详细剧情结构
2. 关键情节点
3. 人物关系发展
4. 冲突设置
5. 高潮和结局安排

请以专业编剧的视角，构建引人入胜的故事情节。"""
            }
        ]
        
        return await self.chat_completion(messages, role="writer", temperature=0.7)
    
    async def generate_text(self, content_type: str, requirements: str, style: str = "") -> Dict[str, Any]:
        """
        生成文本内容
        
        Args:
            content_type: 内容类型
            requirements: 内容要求
            style: 写作风格
            
        Returns:
            文本生成结果
        """
        messages = [
            {
                "role": "user",
                "content": f"""请创作以下类型的文本：

内容类型：{content_type}
具体要求：{requirements}
写作风格：{style}

请提供高质量的文本内容，注意：
1. 语言流畅自然
2. 符合类型特点
3. 满足具体要求
4. 体现指定风格
5. 具有吸引力和可读性

请以专业撰写师的水准完成创作。"""
            }
        ]
        
        return await self.chat_completion(messages, role="writer", temperature=0.8)
    
    async def multi_agent_discussion(self, topic: str, participants: List[str], context: str = "") -> Dict[str, Any]:
        """
        多角色讨论
        
        Args:
            topic: 讨论主题
            participants: 参与者列表
            context: 讨论上下文
            
        Returns:
            讨论结果
        """
        results = {}
        
        for participant in participants:
            if participant in self.role_prompts:
                messages = [
                    {
                        "role": "user",
                        "content": f"""请就以下主题发表你的专业观点：

讨论主题：{topic}
背景信息：{context}

请从你的专业角度提供：
1. 对主题的理解和分析
2. 专业建议和方案
3. 可能的挑战和解决方案
4. 与其他角色的协作建议

请保持你的角色特色，提供有价值的专业意见。"""
                    }
                ]
                
                result = await self.chat_completion(messages, role=participant, temperature=0.7)
                results[participant] = result
        
        return {
            "success": True,
            "topic": topic,
            "participants": participants,
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        测试API连接
        
        Returns:
            连接测试结果
        """
        messages = [
            {
                "role": "user",
                "content": "你好，请简单介绍一下你自己。"
            }
        ]
        
        result = await self.chat_completion(messages, role="director", max_tokens=100)
        
        return {
            "connection_test": result.get("success", False),
            "api_key_valid": result.get("success", False),
            "timestamp": datetime.now().isoformat(),
            "details": result
        }

# 全局客户端实例
_siliconflow_client = None

async def get_siliconflow_client() -> SiliconFlowClient:
    """获取Silicon Flow客户端实例"""
    global _siliconflow_client
    if _siliconflow_client is None:
        _siliconflow_client = SiliconFlowClient()
    return _siliconflow_client

async def cleanup_siliconflow_client():
    """清理Silicon Flow客户端"""
    global _siliconflow_client
    if _siliconflow_client and _siliconflow_client.session:
        await _siliconflow_client.session.close()
        _siliconflow_client = None
