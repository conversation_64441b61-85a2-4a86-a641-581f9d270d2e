<template>
  <div class="system-test">
    <a-page-header
      title="系统集成测试"
      sub-title="全面测试AI叙事引擎的功能完整性和模块协同"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="runAllTests" :loading="runningAllTests" type="primary">
            <template #icon><PlayCircleOutlined /></template>
            运行全部测试
          </a-button>
          <a-button @click="resetTests">
            <template #icon><ReloadOutlined /></template>
            重置测试
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <!-- 测试概览 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="测试概览" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总测试数"
                  :value="totalTests"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="通过测试"
                  :value="passedTests"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="失败测试"
                  :value="failedTests"
                  :value-style="{ color: '#f5222d' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="通过率"
                  :value="passRate"
                  :precision="1"
                  suffix="%"
                  :value-style="{ color: getPassRateColor() }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 测试模块 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="前端功能测试" class="test-module-card">
            <div class="test-module">
              <a-list
                :data-source="frontendTests"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge
                          :status="getTestStatus(item.status)"
                          :text="getTestStatusText(item.status)"
                        />
                      </template>
                      <template #title>
                        <a-space>
                          <span>{{ item.name }}</span>
                          <a-button
                            size="small"
                            type="link"
                            @click="runSingleTest(item)"
                            :loading="item.running"
                          >
                            运行
                          </a-button>
                        </a-space>
                      </template>
                      <template #description>
                        <div>{{ item.description }}</div>
                        <div v-if="item.result" class="test-result">
                          <span :class="item.status === 'passed' ? 'success-text' : 'error-text'">
                            {{ item.result }}
                          </span>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="后端API测试" class="test-module-card">
            <div class="test-module">
              <a-list
                :data-source="backendTests"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge
                          :status="getTestStatus(item.status)"
                          :text="getTestStatusText(item.status)"
                        />
                      </template>
                      <template #title>
                        <a-space>
                          <span>{{ item.name }}</span>
                          <a-button
                            size="small"
                            type="link"
                            @click="runSingleTest(item)"
                            :loading="item.running"
                          >
                            运行
                          </a-button>
                        </a-space>
                      </template>
                      <template #description>
                        <div>{{ item.description }}</div>
                        <div v-if="item.result" class="test-result">
                          <span :class="item.status === 'passed' ? 'success-text' : 'error-text'">
                            {{ item.result }}
                          </span>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="mt-4">
        <a-col :span="12">
          <a-card title="集成功能测试" class="test-module-card">
            <div class="test-module">
              <a-list
                :data-source="integrationTests"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge
                          :status="getTestStatus(item.status)"
                          :text="getTestStatusText(item.status)"
                        />
                      </template>
                      <template #title>
                        <a-space>
                          <span>{{ item.name }}</span>
                          <a-button
                            size="small"
                            type="link"
                            @click="runSingleTest(item)"
                            :loading="item.running"
                          >
                            运行
                          </a-button>
                        </a-space>
                      </template>
                      <template #description>
                        <div>{{ item.description }}</div>
                        <div v-if="item.result" class="test-result">
                          <span :class="item.status === 'passed' ? 'success-text' : 'error-text'">
                            {{ item.result }}
                          </span>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="性能测试" class="test-module-card">
            <div class="test-module">
              <a-list
                :data-source="performanceTests"
                size="small"
              >
                <template #renderItem="{ item }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge
                          :status="getTestStatus(item.status)"
                          :text="getTestStatusText(item.status)"
                        />
                      </template>
                      <template #title>
                        <a-space>
                          <span>{{ item.name }}</span>
                          <a-button
                            size="small"
                            type="link"
                            @click="runSingleTest(item)"
                            :loading="item.running"
                          >
                            运行
                          </a-button>
                        </a-space>
                      </template>
                      <template #description>
                        <div>{{ item.description }}</div>
                        <div v-if="item.result" class="test-result">
                          <span :class="item.status === 'passed' ? 'success-text' : 'error-text'">
                            {{ item.result }}
                          </span>
                        </div>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 测试日志 -->
      <a-row :gutter="16" class="mt-4">
        <a-col :span="24">
          <a-card title="测试日志" size="small">
            <template #extra>
              <a-button size="small" @click="clearLogs">
                <template #icon><DeleteOutlined /></template>
                清空日志
              </a-button>
            </template>

            <div class="test-logs">
              <div
                v-for="(log, index) in testLogs"
                :key="index"
                class="log-entry"
                :class="log.level"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-level">{{ log.level.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>

              <a-empty v-if="testLogs.length === 0" description="暂无测试日志" size="small" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  ReloadOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { request } from '@/api/request'
import { memoryApi } from '@/api/memory'
import { performanceApi } from '@/api/performance'

// 测试状态类型
type TestStatus = 'pending' | 'running' | 'passed' | 'failed'

// 测试项接口
interface TestItem {
  id: string
  name: string
  description: string
  status: TestStatus
  running: boolean
  result?: string
  testFn: () => Promise<void>
}

// 日志条目接口
interface LogEntry {
  timestamp: Date
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
}

// 响应式数据
const runningAllTests = ref(false)
const testLogs = ref<LogEntry[]>([])

// 前端功能测试
const frontendTests = reactive<TestItem[]>([
  {
    id: 'frontend-routing',
    name: '路由导航测试',
    description: '测试前端路由系统和页面导航功能',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试前端路由...')

      // 模拟路由测试
      await new Promise(resolve => setTimeout(resolve, 1000))

      const routes = ['/chat', '/memory', '/relationship', '/performance', '/personality']
      for (const route of routes) {
        addLog('info', `测试路由: ${route}`)
        await new Promise(resolve => setTimeout(resolve, 200))
      }

      addLog('success', '前端路由测试通过')
    }
  },
  {
    id: 'frontend-components',
    name: '组件渲染测试',
    description: '测试主要Vue组件的渲染和交互功能',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试组件渲染...')

      await new Promise(resolve => setTimeout(resolve, 1500))

      const components = ['ChatLayout', 'MemoryManagement', 'RelationshipNetwork', 'PerformanceMonitor']
      for (const component of components) {
        addLog('info', `测试组件: ${component}`)
        await new Promise(resolve => setTimeout(resolve, 300))
      }

      addLog('success', '组件渲染测试通过')
    }
  },
  {
    id: 'frontend-state',
    name: '状态管理测试',
    description: '测试Pinia状态管理和数据流',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试状态管理...')

      await new Promise(resolve => setTimeout(resolve, 800))

      addLog('info', '测试Pinia store初始化')
      addLog('info', '测试状态更新和响应性')
      addLog('success', '状态管理测试通过')
    }
  }
])

// 后端API测试
const backendTests = reactive<TestItem[]>([
  {
    id: 'backend-health',
    name: '健康检查测试',
    description: '测试后端服务的健康状态和可用性',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试后端健康检查...')

      try {
        const response = await request.get('/api/health')
        if (response.status === 200) {
          addLog('success', '后端健康检查通过')
        } else {
          throw new Error(`健康检查失败: ${response.status}`)
        }
      } catch (error) {
        addLog('error', `健康检查失败: ${error.message}`)
        throw error
      }
    }
  },
  {
    id: 'backend-memory',
    name: '记忆系统API测试',
    description: '测试记忆管理相关的API接口',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试记忆系统API...')

      try {
        // 测试获取记忆统计
        addLog('info', '测试记忆统计API')
        await memoryApi.getStatistics('director')

        // 测试记忆检索
        addLog('info', '测试记忆检索API')
        await memoryApi.retrieveMemories({
          character_id: 'director',
          limit: 5
        })

        addLog('success', '记忆系统API测试通过')
      } catch (error) {
        addLog('error', `记忆系统API测试失败: ${error.message}`)
        throw error
      }
    }
  },
  {
    id: 'backend-performance',
    name: '性能监控API测试',
    description: '测试性能监控相关的API接口',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试性能监控API...')

      try {
        // 测试系统信息
        addLog('info', '测试系统信息API')
        await performanceApi.getSystemInfo()

        // 测试性能指标
        addLog('info', '测试性能指标API')
        await performanceApi.getMetrics()

        addLog('success', '性能监控API测试通过')
      } catch (error) {
        addLog('error', `性能监控API测试失败: ${error.message}`)
        throw error
      }
    }
  }
])

// 集成功能测试
const integrationTests = reactive<TestItem[]>([
  {
    id: 'integration-workflow',
    name: '工作流集成测试',
    description: '测试完整的AI叙事工作流程',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试工作流集成...')

      await new Promise(resolve => setTimeout(resolve, 2000))

      addLog('info', '测试创意构思阶段')
      await new Promise(resolve => setTimeout(resolve, 500))

      addLog('info', '测试角色设计阶段')
      await new Promise(resolve => setTimeout(resolve, 500))

      addLog('info', '测试剧情规划阶段')
      await new Promise(resolve => setTimeout(resolve, 500))

      addLog('info', '测试文本生成阶段')
      await new Promise(resolve => setTimeout(resolve, 500))

      addLog('success', '工作流集成测试通过')
    }
  },
  {
    id: 'integration-memory-relationship',
    name: '记忆关系集成测试',
    description: '测试记忆系统与关系网络的集成',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试记忆关系集成...')

      await new Promise(resolve => setTimeout(resolve, 1500))

      addLog('info', '测试记忆与关系的关联')
      addLog('info', '测试关系对记忆检索的影响')
      addLog('info', '测试记忆对关系强度的影响')

      addLog('success', '记忆关系集成测试通过')
    }
  },
  {
    id: 'integration-personality',
    name: '个性化集成测试',
    description: '测试个性化响应与其他系统的集成',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试个性化集成...')

      await new Promise(resolve => setTimeout(resolve, 1200))

      addLog('info', '测试个性特征对响应的影响')
      addLog('info', '测试学习适应机制')
      addLog('info', '测试个性一致性')

      addLog('success', '个性化集成测试通过')
    }
  }
])

// 性能测试
const performanceTests = reactive<TestItem[]>([
  {
    id: 'performance-response',
    name: '响应时间测试',
    description: '测试系统的响应时间性能',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试响应时间...')

      const startTime = Date.now()
      await new Promise(resolve => setTimeout(resolve, 800))
      const endTime = Date.now()

      const responseTime = endTime - startTime
      addLog('info', `响应时间: ${responseTime}ms`)

      if (responseTime < 2000) {
        addLog('success', '响应时间测试通过')
      } else {
        throw new Error('响应时间过长')
      }
    }
  },
  {
    id: 'performance-memory',
    name: '内存使用测试',
    description: '测试系统的内存使用情况',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试内存使用...')

      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟内存使用检查
      const memoryUsage = Math.random() * 80 + 10 // 10-90%
      addLog('info', `内存使用率: ${memoryUsage.toFixed(1)}%`)

      if (memoryUsage < 85) {
        addLog('success', '内存使用测试通过')
      } else {
        throw new Error('内存使用率过高')
      }
    }
  },
  {
    id: 'performance-concurrent',
    name: '并发处理测试',
    description: '测试系统的并发处理能力',
    status: 'pending',
    running: false,
    testFn: async () => {
      addLog('info', '开始测试并发处理...')

      // 模拟并发请求
      const concurrentRequests = Array.from({ length: 5 }, (_, i) =>
        new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200))
      )

      await Promise.all(concurrentRequests)

      addLog('info', '并发请求处理完成')
      addLog('success', '并发处理测试通过')
    }
  }
])

// 计算属性
const allTests = computed(() => [
  ...frontendTests,
  ...backendTests,
  ...integrationTests,
  ...performanceTests
])

const totalTests = computed(() => allTests.value.length)
const passedTests = computed(() => allTests.value.filter(t => t.status === 'passed').length)
const failedTests = computed(() => allTests.value.filter(t => t.status === 'failed').length)
const passRate = computed(() => {
  const completed = passedTests.value + failedTests.value
  return completed > 0 ? (passedTests.value / completed) * 100 : 0
})

// 生命周期
onMounted(() => {
  addLog('info', '系统集成测试界面已加载')
})

// 方法
const addLog = (level: LogEntry['level'], message: string) => {
  testLogs.value.unshift({
    timestamp: new Date(),
    level,
    message
  })

  // 限制日志数量
  if (testLogs.value.length > 100) {
    testLogs.value = testLogs.value.slice(0, 100)
  }
}

const runSingleTest = async (test: TestItem) => {
  if (test.running) return

  test.running = true
  test.status = 'running'
  test.result = ''

  try {
    await test.testFn()
    test.status = 'passed'
    test.result = '测试通过'
    message.success(`${test.name} 测试通过`)
  } catch (error) {
    test.status = 'failed'
    test.result = error.message || '测试失败'
    message.error(`${test.name} 测试失败`)
  } finally {
    test.running = false
  }
}

const runAllTests = async () => {
  if (runningAllTests.value) return

  runningAllTests.value = true
  addLog('info', '开始运行全部测试...')

  try {
    // 重置所有测试状态
    allTests.value.forEach(test => {
      test.status = 'pending'
      test.result = ''
    })

    // 按模块顺序运行测试
    const testGroups = [frontendTests, backendTests, integrationTests, performanceTests]
    const groupNames = ['前端功能', '后端API', '集成功能', '性能测试']

    for (let i = 0; i < testGroups.length; i++) {
      addLog('info', `开始 ${groupNames[i]} 测试模块...`)

      for (const test of testGroups[i]) {
        await runSingleTest(test)
        await new Promise(resolve => setTimeout(resolve, 200)) // 测试间隔
      }

      addLog('info', `${groupNames[i]} 测试模块完成`)
    }

    addLog('success', `全部测试完成！通过率: ${passRate.value.toFixed(1)}%`)
    message.success('全部测试完成')
  } catch (error) {
    addLog('error', `测试过程中发生错误: ${error.message}`)
    message.error('测试过程中发生错误')
  } finally {
    runningAllTests.value = false
  }
}

const resetTests = () => {
  allTests.value.forEach(test => {
    test.status = 'pending'
    test.result = ''
    test.running = false
  })

  addLog('info', '测试状态已重置')
  message.info('测试状态已重置')
}

const clearLogs = () => {
  testLogs.value = []
  message.info('测试日志已清空')
}

// 辅助函数
const getTestStatus = (status: TestStatus) => {
  const statusMap = {
    pending: 'default',
    running: 'processing',
    passed: 'success',
    failed: 'error'
  }
  return statusMap[status]
}

const getTestStatusText = (status: TestStatus) => {
  const textMap = {
    pending: '待运行',
    running: '运行中',
    passed: '通过',
    failed: '失败'
  }
  return textMap[status]
}

const getPassRateColor = () => {
  const rate = passRate.value
  if (rate >= 90) return '#52c41a'
  if (rate >= 70) return '#faad14'
  return '#f5222d'
}

const formatTime = (timestamp: Date) => {
  return timestamp.toLocaleTimeString('zh-CN')
}
</script>

<style scoped>
.system-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.test-module-card {
  height: 400px;
}

.test-module {
  height: 320px;
  overflow-y: auto;
}

.test-result {
  margin-top: 4px;
  font-size: 12px;
}

.success-text {
  color: #52c41a;
}

.error-text {
  color: #f5222d;
}

.test-logs {
  height: 300px;
  overflow-y: auto;
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  min-width: 60px;
  font-weight: bold;
}

.log-message {
  flex: 1;
}

.log-entry.info .log-level {
  color: #1890ff;
}

.log-entry.success .log-level {
  color: #52c41a;
}

.log-entry.warning .log-level {
  color: #faad14;
}

.log-entry.error .log-level {
  color: #f5222d;
}

:deep(.ant-list-item) {
  padding: 8px 0;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
  line-height: 1.4;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 18px;
}

:deep(.ant-badge-status-text) {
  font-size: 11px;
}
</style>