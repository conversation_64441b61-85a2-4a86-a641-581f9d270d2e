/**
 * Silicon Flow API接口
 * 集成真实AI模型的API调用
 */

import { request } from './request'

export interface SiliconFlowResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

export interface CreativeIdeaRequest {
  topic: string
  context?: string
}

export interface CharacterDesignRequest {
  character_info: string
  style_requirements?: string
}

export interface PlotPlanRequest {
  story_outline: string
  requirements?: string
}

export interface TextGenerationRequest {
  content_type: string
  requirements: string
  style?: string
}

export interface MultiAgentDiscussionRequest {
  topic: string
  participants: string[]
  context?: string
}

export interface AIResponse {
  role: string
  model: string
  content: string
  timestamp: string
}

export interface DiscussionResult {
  topic: string
  participants: string[]
  results: Record<string, AIResponse>
  timestamp: string
}

// AI API专用超时配置
const AI_API_TIMEOUT = 90000  // 90秒，适应AI模型推理时间

export const siliconflowApi = {
  // 测试API连接
  async testConnection() {
    return request.get<SiliconFlowResponse>('/api/siliconflow/test', { timeout: AI_API_TIMEOUT })
  },

  // 创意构思生成
  async generateCreativeIdea(data: CreativeIdeaRequest) {
    return request.post<SiliconFlowResponse<AIResponse>>('/api/siliconflow/creative', data, { timeout: AI_API_TIMEOUT })
  },

  // 角色设计
  async designCharacter(data: CharacterDesignRequest) {
    return request.post<SiliconFlowResponse<AIResponse>>('/api/siliconflow/character', data, { timeout: AI_API_TIMEOUT })
  },

  // 剧情规划
  async planPlot(data: PlotPlanRequest) {
    return request.post<SiliconFlowResponse<AIResponse>>('/api/siliconflow/plot', data, { timeout: AI_API_TIMEOUT })
  },

  // 文本生成
  async generateText(data: TextGenerationRequest) {
    return request.post<SiliconFlowResponse<AIResponse>>('/api/siliconflow/text', data, { timeout: AI_API_TIMEOUT })
  },

  // 多角色讨论
  async multiAgentDiscussion(data: MultiAgentDiscussionRequest) {
    return request.post<SiliconFlowResponse<DiscussionResult>>('/api/siliconflow/discussion', data, { timeout: AI_API_TIMEOUT })
  }
}

export default siliconflowApi
