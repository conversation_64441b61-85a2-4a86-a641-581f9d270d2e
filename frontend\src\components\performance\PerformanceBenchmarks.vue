<template>
  <div class="performance-benchmarks">
    <div class="benchmarks-header">
      <a-row :gutter="16" align="middle">
        <a-col :span="12">
          <h5>性能基准测试</h5>
        </a-col>
        <a-col :span="12" style="text-align: right">
          <a-space>
            <a-select
              v-model:value="selectedTestType"
              size="small"
              style="width: 100px"
            >
              <a-select-option value="all">全部测试</a-select-option>
              <a-select-option value="memory">内存测试</a-select-option>
              <a-select-option value="cpu">CPU测试</a-select-option>
              <a-select-option value="io">IO测试</a-select-option>
              <a-select-option value="cache">缓存测试</a-select-option>
            </a-select>
            <a-button size="small" @click="runBenchmark" :loading="benchmarkLoading">
              <template #icon><PlayCircleOutlined /></template>
              运行测试
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <a-divider size="small" />

    <div class="benchmarks-content">
      <a-spin :spinning="loading">
        <div v-if="benchmarkResults.length > 0" class="benchmark-results">
          <a-list
            :data-source="benchmarkResults"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-space>
                      <span class="test-name">{{ getTestDisplayName(item.test_name) }}</span>
                      <a-tag
                        :color="getPerformanceColor(item.success_rate)"
                        size="small"
                      >
                        {{ (item.success_rate * 100).toFixed(1) }}%
                      </a-tag>
                    </a-space>
                  </template>
                  <template #description>
                    <a-row :gutter="8">
                      <a-col :span="8">
                        <span class="metric-label">耗时:</span>
                        <span class="metric-value">{{ item.duration.toFixed(3) }}s</span>
                      </a-col>
                      <a-col :span="8">
                        <span class="metric-label">OPS:</span>
                        <span class="metric-value">{{ formatOps(item.operations_per_second) }}</span>
                      </a-col>
                      <a-col :span="8">
                        <span class="metric-label">内存:</span>
                        <span class="metric-value">{{ formatBytes(item.memory_usage) }}</span>
                      </a-col>
                    </a-row>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <span class="test-time">{{ formatTime(item.timestamp) }}</span>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>

        <div v-else class="no-benchmarks">
          <a-empty description="暂无基准测试结果" size="small">
            <template #image>
              <ExperimentOutlined style="font-size: 48px; color: #d9d9d9" />
            </template>
            <a-button size="small" type="primary" @click="runBenchmark">
              运行首次基准测试
            </a-button>
          </a-empty>
        </div>
      </a-spin>
    </div>

    <a-divider size="small" />

    <div class="benchmark-history">
      <a-collapse ghost size="small">
        <a-collapse-panel header="查看历史记录" key="history">
          <div v-if="historyResults.length > 0" class="history-list">
            <a-timeline size="small">
              <a-timeline-item
                v-for="result in historyResults"
                :key="result.timestamp"
                :color="getPerformanceColor(result.success_rate)"
              >
                <div class="history-item">
                  <div class="history-header">
                    <span class="history-test">{{ getTestDisplayName(result.test_name) }}</span>
                    <span class="history-time">{{ formatDateTime(result.timestamp) }}</span>
                  </div>
                  <div class="history-metrics">
                    <a-space size="small">
                      <a-tag size="small">{{ result.duration.toFixed(3) }}s</a-tag>
                      <a-tag size="small">{{ formatOps(result.operations_per_second) }} OPS</a-tag>
                      <a-tag size="small">{{ (result.success_rate * 100).toFixed(1) }}%</a-tag>
                    </a-space>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
          <a-empty v-else description="暂无历史记录" size="small" />
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  ExperimentOutlined
} from '@ant-design/icons-vue'
import { performanceApi } from '@/api/performance'

// 响应式数据
const loading = ref(false)
const benchmarkLoading = ref(false)
const selectedTestType = ref('all')
const benchmarkResults = ref<any[]>([])
const historyResults = ref<any[]>([])

// 生命周期
onMounted(() => {
  loadBenchmarkHistory()
})

// 方法
const runBenchmark = async () => {
  benchmarkLoading.value = true
  try {
    const response = await performanceApi.runBenchmark(selectedTestType.value)
    benchmarkResults.value = response.data || []
    
    message.success('基准测试完成')
    
    // 刷新历史记录
    loadBenchmarkHistory()
  } catch (error) {
    message.error('基准测试失败: ' + error.message)
  } finally {
    benchmarkLoading.value = false
  }
}

const loadBenchmarkHistory = async () => {
  loading.value = true
  try {
    const response = await performanceApi.getBenchmarkHistory(10)
    historyResults.value = response.data || []
  } catch (error) {
    console.error('加载基准测试历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 辅助函数
const getTestDisplayName = (testName: string) => {
  const nameMap = {
    'memory_test': '内存测试',
    'cpu_test': 'CPU测试',
    'io_test': 'IO测试',
    'cache_test': '缓存测试',
    'database_test': '数据库测试',
    'api_test': 'API测试',
    'overall_test': '综合测试'
  }
  return nameMap[testName] || testName
}

const getPerformanceColor = (successRate: number) => {
  if (successRate >= 0.95) return 'green'
  if (successRate >= 0.8) return 'blue'
  if (successRate >= 0.6) return 'orange'
  return 'red'
}

const formatOps = (ops: number) => {
  if (ops >= 1000000) {
    return (ops / 1000000).toFixed(1) + 'M'
  } else if (ops >= 1000) {
    return (ops / 1000).toFixed(1) + 'K'
  }
  return ops.toFixed(0)
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const formatDateTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style scoped>
.performance-benchmarks {
  padding: 8px;
}

.benchmarks-header h5 {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.benchmark-results {
  max-height: 200px;
  overflow-y: auto;
}

.test-name {
  font-weight: 500;
  color: #262626;
}

.metric-label {
  font-size: 11px;
  color: #999;
  margin-right: 4px;
}

.metric-value {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.test-time {
  font-size: 11px;
  color: #999;
}

.no-benchmarks {
  text-align: center;
  padding: 20px;
}

.history-list {
  max-height: 150px;
  overflow-y: auto;
}

.history-item {
  font-size: 12px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.history-test {
  font-weight: 500;
  color: #262626;
}

.history-time {
  color: #999;
  font-size: 11px;
}

.history-metrics {
  margin-top: 4px;
}

:deep(.ant-list-item) {
  padding: 8px 0;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
}

:deep(.ant-list-item-meta-description) {
  font-size: 11px;
}

:deep(.ant-timeline-item-content) {
  min-height: auto;
}

:deep(.ant-collapse-header) {
  padding: 4px 0 !important;
  font-size: 12px;
}

:deep(.ant-collapse-content-box) {
  padding: 4px 0 !important;
}

:deep(.ant-empty-description) {
  font-size: 12px;
}
</style>
