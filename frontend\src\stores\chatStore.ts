import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ChatMessage {
  id: string
  sender: string
  content: string
  type: 'user' | 'agent' | 'system'
  timestamp: number
  agentType?: string
}

export const useChatStore = defineStore('chat', () => {
  const messages = ref<ChatMessage[]>([
    {
      id: '1',
      sender: '系统',
      content: '欢迎使用群聊式AI叙事引擎！您可以使用 @导演、@设计师、@撰写师 来呼叫AI助手，使用 +角色名 添加角色，使用 -角色名 移除角色。',
      type: 'system',
      timestamp: Date.now() - 60000
    }
  ])
  const currentStoryId = ref<string>('')
  const isLoading = ref(false)

  // 添加消息
  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now()
    }
    messages.value.push(newMessage)
    console.log('✅ 消息已添加到store:', newMessage)
    console.log('📊 当前消息总数:', messages.value.length)
  }

  // 发送用户消息
  const sendMessage = (content: string) => {
    addMessage({
      sender: '用户',
      content,
      type: 'user'
    })

    // 解析@mention命令
    const mentions = parseMentions(content)
    if (mentions.length > 0) {
      // 发送到后端处理
      handleMentionCommands(mentions, content)
    }
  }

  // 解析@mention命令
  const parseMentions = (content: string) => {
    const mentionRegex = /[@+\-](\w+)/g
    const mentions = []
    let match

    while ((match = mentionRegex.exec(content)) !== null) {
      const [fullMatch, target] = match
      const type = fullMatch[0] === '@' ? 'agent' : 
                   fullMatch[0] === '+' ? 'add_character' : 'remove_character'
      
      mentions.push({
        type,
        target,
        fullMatch
      })
    }

    return mentions
  }

  // 处理mention命令 - 修复版本，支持Socket.IO和HTTP API双重模式
  const handleMentionCommands = async (mentions: any[], content: string) => {
    isLoading.value = true

    try {
      console.log('处理mention命令:', mentions, content)

      // 首先尝试使用HTTP API（更可靠）
      const success = await tryHttpApiMention(mentions, content)

      if (success) {
        console.log('HTTP API处理成功')
        return
      }

      // HTTP API失败，尝试Socket.IO
      console.log('HTTP API失败，尝试Socket.IO...')
      await trySocketIOMention(mentions, content)

    } catch (error) {
      console.error('处理mention命令失败:', error)
      addMessage({
        sender: '系统',
        content: '命令处理失败，请重试: ' + error.message,
        type: 'system'
      })
      isLoading.value = false
    }
  }

  // 使用HTTP API处理@mention
  const tryHttpApiMention = async (mentions: any[], content: string): Promise<boolean> => {
    try {
      // 导入Silicon Flow API
      const { siliconflowApi } = await import('@/api/siliconflow')

      // 解析@mention的角色
      const agentMentions = mentions.filter(m => m.type === 'agent')

      if (agentMentions.length === 0) {
        return false
      }

      // 处理第一个@mention的角色
      const firstMention = agentMentions[0]
      const agentName = firstMention.target

      // 映射中文角色名到API类型
      const roleMapping: Record<string, string> = {
        '导演': 'creative',
        '设计师': 'character',
        '撰写师': 'text'
      }

      const apiType = roleMapping[agentName]
      if (!apiType) {
        console.log('未知的角色:', agentName)
        return false
      }

      // 提取用户指令（去掉@mention部分）
      const userCommand = content.replace(/@\w+\s*/, '').trim()

      let response
      const startTime = Date.now()

      // 根据角色类型调用相应的API
      switch (apiType) {
        case 'creative':
          response = await siliconflowApi.generateCreativeIdea({
            topic: userCommand,
            context: '用户通过@导演功能发起的创意请求'
          })
          break

        case 'character':
          response = await siliconflowApi.designCharacter({
            character_info: userCommand,
            style_requirements: '根据用户需求设计'
          })
          break

        case 'text':
          response = await siliconflowApi.generateText({
            content_type: '创意文本',
            requirements: userCommand,
            style: '专业撰写师风格'
          })
          break

        default:
          return false
      }

      const responseTime = Date.now() - startTime

      if (response.data.success) {
        // 提取AI响应内容
        let aiContent = ''
        if (response.data.data?.choices?.[0]?.message?.content) {
          aiContent = response.data.data.choices[0].message.content
        } else if (response.data.data?.content) {
          aiContent = response.data.data.content
        } else {
          aiContent = '抱歉，我没有生成有效的响应内容。'
        }

        // 添加AI响应消息
        addMessage({
          sender: agentName,
          content: aiContent,
          type: 'agent',
          agentType: agentName
        })

        console.log(`✅ ${agentName}响应成功，耗时${responseTime}ms`)
        isLoading.value = false
        return true

      } else {
        console.error('API响应失败:', response.data.error)

        // 如果是503错误（服务器繁忙），提供本地模拟响应
        if (response.data.error?.includes('503') || response.data.error?.includes('too busy')) {
          console.log('检测到服务器繁忙，使用本地模拟响应')
          const fallbackContent = generateFallbackResponse(agentName, userCommand)

          addMessage({
            sender: agentName,
            content: fallbackContent,
            type: 'agent',
            agentType: agentName
          })

          console.log(`⚠️ ${agentName}使用本地模拟响应，耗时${responseTime}ms`)
          isLoading.value = false
          return true
        }

        return false
      }

    } catch (error) {
      console.error('HTTP API处理失败:', error)

      // 获取角色名（如果在catch块中）
      const agentMentions = mentions.filter(m => m.type === 'agent')
      if (agentMentions.length > 0) {
        const firstMention = agentMentions[0]
        const agentName = firstMention.target
        const userCommand = content.replace(/@\w+\s*/, '').trim()

        // 检查是否是超时错误
        if (error.message?.includes('timeout') || error.message?.includes('Request timeout')) {
          console.log('检测到超时错误，使用本地模拟响应')
          const fallbackContent = generateFallbackResponse(agentName, userCommand)

          addMessage({
            sender: agentName,
            content: fallbackContent,
            type: 'agent',
            agentType: agentName
          })

          console.log(`⚠️ ${agentName}因超时使用本地模拟响应`)
          isLoading.value = false
          return true
        }
      }

      return false
    }
  }

  // 生成本地模拟响应（最终备用方案）
  const generateFallbackResponse = (agentName: string, userCommand: string): string => {
    const responses = {
      '导演': [
        `作为导演，我认为"${userCommand}"是一个很有潜力的创意方向。让我为你分析一下：

🎬 **创意分析**：
这个主题具有很强的视觉表现力和情感张力，可以吸引观众的注意力。

🎯 **目标受众**：
适合喜欢深度思考和情感共鸣的观众群体。

📝 **发展建议**：
1. 深入挖掘角色的内心冲突
2. 构建引人入胜的故事节奏
3. 注重视觉效果与情感表达的平衡

💡 **下一步**：
建议先完善角色设定，然后制定详细的剧情大纲。

*注：当前使用本地响应模式，AI服务暂时繁忙*`,

        `关于"${userCommand}"，我作为导演想分享一些专业见解：

🎭 **故事核心**：
每个好故事都需要一个强有力的核心冲突，这是推动剧情发展的引擎。

🎨 **视觉风格**：
建议采用现代简约的视觉语言，突出主题的深度和内涵。

⭐ **角色塑造**：
主角应该具有鲜明的个性特征和成长弧线。

🎵 **节奏把控**：
注意故事的起承转合，确保观众始终保持兴趣。

*注：AI服务繁忙中，这是临时响应*`
      ],

      '设计师': [
        `作为设计师，我对"${userCommand}"有以下设计构思：

🎨 **视觉概念**：
采用现代简约风格，突出功能性与美观性的完美结合。

🌈 **色彩方案**：
建议使用温暖的色调，营造舒适友好的视觉体验。

✨ **设计元素**：
- 简洁的线条设计
- 富有层次感的布局
- 符合人体工程学的结构

🔧 **实现方案**：
1. 先绘制概念草图
2. 制作3D模型预览
3. 优化细节和材质

*注：AI设计服务暂时不可用，这是备用响应*`,

        `关于"${userCommand}"的设计方案：

🎯 **设计理念**：
以用户体验为中心，追求简约而不简单的设计哲学。

📐 **结构设计**：
采用模块化设计思路，便于后期调整和优化。

🎪 **风格定位**：
现代、简洁、富有科技感，符合当代审美趋势。

🛠️ **制作工艺**：
选用优质材料，注重细节处理和质感表现。

*注：设计AI暂时繁忙，使用本地创意库*`
      ],

      '撰写师': [
        `关于"${userCommand}"，我来为你创作一段内容：

📝 **创作思路**：
以细腻的笔触描绘情感，用生动的语言构建画面感。

✍️ **文本内容**：
在这个充满可能性的世界里，每一个想法都像种子一样，等待着合适的土壤和阳光。"${userCommand}"正是这样一颗充满潜力的种子，它承载着创作者的梦想和观众的期待。

通过精心的构思和巧妙的表达，我们可以让这个创意绽放出独特的光芒，触动人心，引发共鸣。

🎨 **写作技巧**：
- 运用比喻和象征手法
- 注重情感的层次递进
- 保持语言的节奏感

*注：AI写作助手繁忙中，这是创意储备*`,

        `针对"${userCommand}"的文本创作：

🌟 **开篇构思**：
好的开头如同黎明的第一缕阳光，能够瞬间抓住读者的心。

📖 **内容展开**：
故事如河流般蜿蜒前行，每一个转折都蕴含着深意。在"${userCommand}"这个主题下，我们可以探索人性的复杂与美好，描绘生活的真实与梦想。

文字是有温度的，它们能够跨越时空的界限，在读者心中种下情感的种子。

🎭 **结尾升华**：
让故事在高潮中戛然而止，留给读者无限的想象空间。

*注：写作AI暂时不可用，使用本地文库*`
      ]
    }

    const agentResponses = responses[agentName] || responses['导演']
    const randomIndex = Math.floor(Math.random() * agentResponses.length)
    return agentResponses[randomIndex]
  }

  // 使用Socket.IO处理@mention（备用方案）
  const trySocketIOMention = async (mentions: any[], content: string) => {
    try {
      const { socketStore } = await import('./socketStore')
      const socket = socketStore()

      // 确保Socket连接
      if (socket.connectionStatus !== 'connected') {
        socket.connect()
        // 等待连接建立
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('Socket连接超时')), 5000)

          const checkConnection = () => {
            if (socket.connectionStatus === 'connected') {
              clearTimeout(timeout)
              resolve(true)
            } else if (socket.connectionStatus === 'error') {
              clearTimeout(timeout)
              reject(new Error('Socket连接失败'))
            } else {
              setTimeout(checkConnection, 100)
            }
          }
          checkConnection()
        })
      }

      // 加入默认故事会话
      const storyId = 'default_story'
      const userId = 'user_' + Date.now()
      socket.joinStorySession(storyId, userId)

      // 发送聊天消息（包含@mention）
      socket.sendChatMessage(content, storyId, userId)

      // 监听Agent响应
      socket.socket?.on('agent_response', (data: any) => {
        console.log('收到Agent响应:', data)
        addMessage({
          sender: data.agent_name || data.sender,
          content: data.response || data.content,
          type: 'agent',
          agentType: data.agent_name
        })
        isLoading.value = false
      })

      // 监听新消息
      socket.socket?.on('new_message', (data: any) => {
        console.log('收到新消息:', data)
        if (data.type === 'agent' || data.type === 'system') {
          addMessage({
            sender: data.sender,
            content: data.content,
            type: data.type,
            agentType: data.agentType
          })
        }
      })

      // 设置超时处理（比API超时时间稍长）
      setTimeout(() => {
        if (isLoading.value) {
          isLoading.value = false
          addMessage({
            sender: '系统',
            content: 'AI响应超时，请重试。如果问题持续，系统会自动使用本地响应。',
            type: 'system'
          })
        }
      }, 100000) // 100秒超时，比API的90秒稍长

    } catch (error) {
      console.error('Socket.IO处理失败:', error)
      isLoading.value = false
      addMessage({
        sender: '系统',
        content: 'Socket连接失败，请检查网络连接',
        type: 'system'
      })
    }
  }

  // 清空消息
  const clearMessages = () => {
    messages.value = []
  }

  // 设置故事ID
  const setStoryId = (storyId: string) => {
    currentStoryId.value = storyId
  }

  // 计算属性
  const messageCount = computed(() => messages.value.length)
  const lastMessage = computed(() => messages.value[messages.value.length - 1])

  return {
    messages,
    currentStoryId,
    isLoading,
    messageCount,
    lastMessage,
    addMessage,
    sendMessage,
    clearMessages,
    setStoryId,
    parseMentions,
    handleMentionCommands,
    tryHttpApiMention,
    trySocketIOMention,
    generateFallbackResponse
  }
})
