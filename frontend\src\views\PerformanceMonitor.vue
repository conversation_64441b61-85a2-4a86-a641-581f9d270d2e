<template>
  <div class="performance-monitor">
    <a-page-header
      title="性能监控"
      sub-title="实时监控系统性能指标和优化建议"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-switch
            v-model:checked="autoRefresh"
            checked-children="自动刷新"
            un-checked-children="手动刷新"
            @change="onAutoRefreshChange"
          />
          <a-button @click="refreshAll">
            <template #icon><ReloadOutlined /></template>
            刷新全部
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="monitor-content">
      <!-- 系统健康状态 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="系统健康状态" :loading="healthLoading">
            <template #extra>
              <a-badge
                :status="getHealthStatus(healthData?.health_status)"
                :text="getHealthStatusText(healthData?.health_status)"
              />
            </template>

            <div v-if="healthData" class="health-overview">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic
                    title="系统状态"
                    :value="healthData.health_status"
                    :value-style="{ color: getHealthColor(healthData.health_status) }"
                  />
                </a-col>
                <a-col :span="6">
                  <a-statistic
                    title="检查时间"
                    :value="formatTime(healthData.timestamp)"
                    :value-style="{ color: '#666' }"
                  />
                </a-col>
                <a-col :span="12">
                  <div class="health-issues">
                    <h5>当前问题:</h5>
                    <a-space wrap v-if="healthData.issues?.length">
                      <a-tag
                        v-for="issue in healthData.issues"
                        :key="issue"
                        color="red"
                        size="small"
                      >
                        {{ issue }}
                      </a-tag>
                    </a-space>
                    <span v-else class="no-issues">✅ 系统运行正常</span>
                  </div>
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 实时性能指标 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="12">
          <a-card title="实时性能指标" :loading="metricsLoading">
            <template #extra>
              <a-tag color="blue">实时数据</a-tag>
            </template>

            <div v-if="metricsData" class="metrics-grid">
              <a-row :gutter="16">
                <a-col :span="12">
                  <div class="metric-item">
                    <h5>响应时间</h5>
                    <a-progress
                      :percent="Math.min((metricsData.response_time / 2) * 100, 100)"
                      :stroke-color="getResponseTimeColor(metricsData.response_time)"
                      :format="() => `${metricsData.response_time.toFixed(2)}s`"
                    />
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="metric-item">
                    <h5>内存使用率</h5>
                    <a-progress
                      :percent="metricsData.memory_usage"
                      :stroke-color="getMemoryColor(metricsData.memory_usage)"
                      :format="(percent) => `${percent}%`"
                    />
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="metric-item">
                    <h5>CPU使用率</h5>
                    <a-progress
                      :percent="metricsData.cpu_usage"
                      :stroke-color="getCpuColor(metricsData.cpu_usage)"
                      :format="(percent) => `${percent}%`"
                    />
                  </div>
                </a-col>
                <a-col :span="12">
                  <div class="metric-item">
                    <h5>缓存命中率</h5>
                    <a-progress
                      :percent="metricsData.cache_hit_rate * 100"
                      :stroke-color="getCacheColor(metricsData.cache_hit_rate)"
                      :format="(percent) => `${percent}%`"
                    />
                  </div>
                </a-col>
              </a-row>

              <a-divider size="small" />

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-statistic
                    title="并发请求"
                    :value="metricsData.concurrent_requests"
                    suffix="个"
                    :value-style="{ color: '#1890ff' }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="错误率"
                    :value="(metricsData.error_rate * 100).toFixed(2)"
                    suffix="%"
                    :value-style="{ color: getErrorRateColor(metricsData.error_rate) }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="更新时间"
                    :value="formatTime(metricsData.timestamp)"
                    :value-style="{ color: '#666' }"
                  />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="系统信息" :loading="systemLoading">
            <div v-if="systemData" class="system-info">
              <a-descriptions :column="1" size="small" bordered>
                <a-descriptions-item label="操作系统">
                  {{ systemData.platform }}
                </a-descriptions-item>
                <a-descriptions-item label="Python版本">
                  {{ systemData.python_version }}
                </a-descriptions-item>
                <a-descriptions-item label="CPU核心数">
                  {{ systemData.cpu_count }}
                </a-descriptions-item>
                <a-descriptions-item label="总内存">
                  {{ formatBytes(systemData.memory.total) }}
                </a-descriptions-item>
                <a-descriptions-item label="可用内存">
                  {{ formatBytes(systemData.memory.available) }}
                </a-descriptions-item>
                <a-descriptions-item label="磁盘使用">
                  {{ systemData.disk.percent }}% ({{ formatBytes(systemData.disk.used) }}/{{ formatBytes(systemData.disk.total) }})
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 性能摘要和优化建议 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="12">
          <a-card title="性能摘要" :loading="summaryLoading">
            <div v-if="summaryData" class="performance-summary">
              <a-tabs size="small">
                <a-tab-pane key="current" tab="当前指标">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <a-statistic
                        title="响应时间"
                        :value="summaryData.current_metrics?.response_time || 0"
                        :precision="3"
                        suffix="s"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="内存使用"
                        :value="summaryData.current_metrics?.memory_usage || 0"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="CPU使用"
                        :value="summaryData.current_metrics?.cpu_usage || 0"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="缓存命中"
                        :value="(summaryData.current_metrics?.cache_hit_rate || 0) * 100"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                  </a-row>
                </a-tab-pane>

                <a-tab-pane key="average" tab="平均值">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <a-statistic
                        title="平均响应时间"
                        :value="summaryData.averages?.avg_response_time || 0"
                        :precision="3"
                        suffix="s"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="平均内存使用"
                        :value="summaryData.averages?.avg_memory_usage || 0"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="平均CPU使用"
                        :value="summaryData.averages?.avg_cpu_usage || 0"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="平均缓存命中"
                        :value="(summaryData.averages?.avg_cache_hit_rate || 0) * 100"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                  </a-row>
                </a-tab-pane>

                <a-tab-pane key="cache" tab="缓存统计">
                  <a-row :gutter="8">
                    <a-col :span="12">
                      <a-statistic
                        title="缓存条目"
                        :value="summaryData.cache_stats?.total_entries || 0"
                        suffix="个"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="命中次数"
                        :value="summaryData.cache_stats?.hits || 0"
                        suffix="次"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="未命中次数"
                        :value="summaryData.cache_stats?.misses || 0"
                        suffix="次"
                        size="small"
                      />
                    </a-col>
                    <a-col :span="12">
                      <a-statistic
                        title="命中率"
                        :value="(summaryData.cache_stats?.hit_rate || 0) * 100"
                        :precision="1"
                        suffix="%"
                        size="small"
                      />
                    </a-col>
                  </a-row>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="优化建议" :loading="recommendationsLoading">
            <div v-if="recommendationsData" class="recommendations">
              <a-list
                :data-source="recommendationsData"
                size="small"
              >
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <a-list-item-meta>
                      <template #avatar>
                        <a-badge
                          :count="index + 1"
                          :number-style="{ backgroundColor: getRecommendationColor(index) }"
                        />
                      </template>
                      <template #title>
                        <span class="recommendation-text">{{ item }}</span>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>

              <div v-if="recommendationsData.length === 0" class="no-recommendations">
                <a-result
                  status="success"
                  title="系统运行良好"
                  sub-title="当前没有优化建议"
                >
                  <template #icon>
                    <CheckCircleOutlined style="color: #52c41a" />
                  </template>
                </a-result>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 性能基准和优化提示 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="性能基准" size="small">
            <PerformanceBenchmarks />
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="优化提示" size="small">
            <OptimizationTips />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import PerformanceBenchmarks from '@/components/performance/PerformanceBenchmarks.vue'
import OptimizationTips from '@/components/performance/OptimizationTips.vue'
import { performanceApi } from '@/api/performance'

// 响应式数据
const autoRefresh = ref(true)
const refreshInterval = ref<number | null>(null)

const healthLoading = ref(false)
const metricsLoading = ref(false)
const systemLoading = ref(false)
const summaryLoading = ref(false)
const recommendationsLoading = ref(false)

const healthData = ref<any>(null)
const metricsData = ref<any>(null)
const systemData = ref<any>(null)
const summaryData = ref<any>(null)
const recommendationsData = ref<string[]>([])

// 生命周期
onMounted(() => {
  loadAllData()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})

// 方法
const loadAllData = async () => {
  await Promise.all([
    loadHealthData(),
    loadMetricsData(),
    loadSystemData(),
    loadSummaryData(),
    loadRecommendations()
  ])
}

const loadHealthData = async () => {
  healthLoading.value = true
  try {
    const response = await performanceApi.getHealthCheck()
    healthData.value = response.data
  } catch (error) {
    message.error('加载健康状态失败: ' + error.message)
  } finally {
    healthLoading.value = false
  }
}

const loadMetricsData = async () => {
  metricsLoading.value = true
  try {
    const response = await performanceApi.getMetrics()
    metricsData.value = response.data
  } catch (error) {
    message.error('加载性能指标失败: ' + error.message)
  } finally {
    metricsLoading.value = false
  }
}

const loadSystemData = async () => {
  systemLoading.value = true
  try {
    const response = await performanceApi.getSystemInfo()
    systemData.value = response.data
  } catch (error) {
    message.error('加载系统信息失败: ' + error.message)
  } finally {
    systemLoading.value = false
  }
}

const loadSummaryData = async () => {
  summaryLoading.value = true
  try {
    const response = await performanceApi.getSummary()
    summaryData.value = response.data
  } catch (error) {
    message.error('加载性能摘要失败: ' + error.message)
  } finally {
    summaryLoading.value = false
  }
}

const loadRecommendations = async () => {
  recommendationsLoading.value = true
  try {
    const response = await performanceApi.getRecommendations()
    recommendationsData.value = response.data?.recommendations || []
  } catch (error) {
    message.error('加载优化建议失败: ' + error.message)
  } finally {
    recommendationsLoading.value = false
  }
}

const refreshAll = () => {
  loadAllData()
}

const onAutoRefreshChange = (checked: boolean) => {
  if (checked) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshInterval.value) return
  
  refreshInterval.value = window.setInterval(() => {
    loadAllData()
  }, 10000) // 每10秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 辅助函数
const formatTime = (timestamp: string) => {
  if (!timestamp) return '--'
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getHealthStatus = (status: string) => {
  const statusMap = {
    'healthy': 'success',
    'warning': 'warning',
    'critical': 'error'
  }
  return statusMap[status] || 'default'
}

const getHealthStatusText = (status: string) => {
  const textMap = {
    'healthy': '健康',
    'warning': '警告',
    'critical': '严重'
  }
  return textMap[status] || '未知'
}

const getHealthColor = (status: string) => {
  const colorMap = {
    'healthy': '#52c41a',
    'warning': '#faad14',
    'critical': '#f5222d'
  }
  return colorMap[status] || '#666'
}

const getResponseTimeColor = (time: number) => {
  if (time > 2) return '#f5222d'
  if (time > 1) return '#faad14'
  return '#52c41a'
}

const getMemoryColor = (usage: number) => {
  if (usage > 85) return '#f5222d'
  if (usage > 70) return '#faad14'
  return '#52c41a'
}

const getCpuColor = (usage: number) => {
  if (usage > 80) return '#f5222d'
  if (usage > 60) return '#faad14'
  return '#52c41a'
}

const getCacheColor = (rate: number) => {
  if (rate < 0.4) return '#f5222d'
  if (rate < 0.6) return '#faad14'
  return '#52c41a'
}

const getErrorRateColor = (rate: number) => {
  if (rate > 0.05) return '#f5222d'
  if (rate > 0.01) return '#faad14'
  return '#52c41a'
}

const getRecommendationColor = (index: number) => {
  const colors = ['#f5222d', '#faad14', '#1890ff', '#52c41a', '#722ed1']
  return colors[index % colors.length]
}
</script>

<style scoped>
.performance-monitor {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.monitor-content {
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.health-overview {
  padding: 8px 0;
}

.health-issues h5 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
}

.no-issues {
  color: #52c41a;
  font-size: 13px;
}

.metrics-grid {
  padding: 8px 0;
}

.metric-item {
  margin-bottom: 16px;
}

.metric-item h5 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
}

.system-info {
  padding: 8px 0;
}

.performance-summary {
  padding: 8px 0;
}

.recommendations {
  max-height: 300px;
  overflow-y: auto;
}

.recommendation-text {
  font-size: 13px;
  line-height: 1.5;
}

.no-recommendations {
  text-align: center;
  padding: 20px;
}

:deep(.ant-progress-text) {
  font-size: 12px;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  width: 100px;
}

:deep(.ant-list-item) {
  padding: 8px 0;
}

:deep(.ant-list-item-meta) {
  align-items: flex-start;
}

:deep(.ant-tabs-content-holder) {
  padding-top: 8px;
}

:deep(.ant-result-title) {
  font-size: 16px;
}

:deep(.ant-result-subtitle) {
  font-size: 12px;
}
</style>
