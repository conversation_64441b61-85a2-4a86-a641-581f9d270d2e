<template>
  <div class="personality-settings">
    <a-page-header
      title="个性化设置"
      sub-title="查看和调整AI角色的个性特征和行为模式"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="resetToDefaults">
            <template #icon><UndoOutlined /></template>
            重置默认
          </a-button>
          <a-button type="primary" @click="savePersonalityChanges" :loading="saveLoading">
            <template #icon><SaveOutlined /></template>
            保存设置
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="settings-content">
      <!-- 角色选择 -->
      <a-row :gutter="16" class="mb-4">
        <a-col :span="24">
          <a-card title="角色选择" size="small">
            <a-radio-group
              v-model:value="selectedCharacter"
              @change="onCharacterChange"
              button-style="solid"
            >
              <a-radio-button value="director">
                <UserOutlined />
                导演
              </a-radio-button>
              <a-radio-button value="designer">
                <BgColorsOutlined />
                设计师
              </a-radio-button>
              <a-radio-button value="writer">
                <EditOutlined />
                撰写师
              </a-radio-button>
            </a-radio-group>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" v-if="personalityData">
        <!-- 五大人格特征 -->
        <a-col :span="12">
          <a-card title="五大人格特征" :loading="personalityLoading">
            <template #extra>
              <a-tooltip title="基于五大人格理论的特征评估">
                <InfoCircleOutlined />
              </a-tooltip>
            </template>

            <div class="personality-traits">
              <div
                v-for="(value, trait) in personalityData.personality_traits"
                :key="trait"
                class="trait-item"
              >
                <div class="trait-header">
                  <span class="trait-name">{{ getTraitName(trait) }}</span>
                  <span class="trait-value">{{ value.toFixed(2) }}</span>
                </div>
                <a-slider
                  v-model:value="personalityData.personality_traits[trait]"
                  :min="0"
                  :max="1"
                  :step="0.01"
                  :tooltip-formatter="(value) => `${value.toFixed(2)}`"
                  @change="onTraitChange(trait, $event)"
                />
                <div class="trait-description">
                  {{ getTraitDescription(trait, value) }}
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 响应风格 -->
        <a-col :span="12">
          <a-card title="响应风格" :loading="personalityLoading">
            <div class="response-styles">
              <a-form layout="vertical">
                <a-form-item label="表达风格">
                  <a-select
                    v-model:value="personalityData.response_style.expression_style"
                    @change="onStyleChange"
                  >
                    <a-select-option value="formal">正式</a-select-option>
                    <a-select-option value="casual">随意</a-select-option>
                    <a-select-option value="enthusiastic">热情</a-select-option>
                    <a-select-option value="analytical">分析性</a-select-option>
                    <a-select-option value="creative">创意性</a-select-option>
                    <a-select-option value="supportive">支持性</a-select-option>
                    <a-select-option value="direct">直接</a-select-option>
                    <a-select-option value="diplomatic">外交性</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="详细程度">
                  <a-slider
                    v-model:value="personalityData.response_style.detail_level"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :marks="{ 0: '简洁', 0.5: '适中', 1: '详细' }"
                    @change="onStyleChange"
                  />
                </a-form-item>

                <a-form-item label="情感表达">
                  <a-slider
                    v-model:value="personalityData.response_style.emotional_expression"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :marks="{ 0: '理性', 0.5: '平衡', 1: '感性' }"
                    @change="onStyleChange"
                  />
                </a-form-item>

                <a-form-item label="创新倾向">
                  <a-slider
                    v-model:value="personalityData.response_style.creativity_bias"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :marks="{ 0: '保守', 0.5: '平衡', 1: '创新' }"
                    @change="onStyleChange"
                  />
                </a-form-item>
              </a-form>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="mt-4" v-if="personalityData">
        <!-- 行为偏好 -->
        <a-col :span="12">
          <a-card title="行为偏好" size="small">
            <div class="behavior-preferences">
              <a-form layout="vertical" size="small">
                <a-form-item label="决策风格">
                  <a-radio-group
                    v-model:value="personalityData.behavior_preferences.decision_style"
                    @change="onBehaviorChange"
                  >
                    <a-radio value="analytical">分析型</a-radio>
                    <a-radio value="intuitive">直觉型</a-radio>
                    <a-radio value="collaborative">协作型</a-radio>
                    <a-radio value="directive">指导型</a-radio>
                  </a-radio-group>
                </a-form-item>

                <a-form-item label="沟通偏好">
                  <a-checkbox-group
                    v-model:value="personalityData.behavior_preferences.communication_preferences"
                    @change="onBehaviorChange"
                  >
                    <a-checkbox value="visual">视觉化</a-checkbox>
                    <a-checkbox value="detailed">详细说明</a-checkbox>
                    <a-checkbox value="examples">举例说明</a-checkbox>
                    <a-checkbox value="structured">结构化</a-checkbox>
                  </a-checkbox-group>
                </a-form-item>

                <a-form-item label="工作节奏">
                  <a-slider
                    v-model:value="personalityData.behavior_preferences.work_pace"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :marks="{ 0: '慢节奏', 0.5: '适中', 1: '快节奏' }"
                    @change="onBehaviorChange"
                  />
                </a-form-item>
              </a-form>
            </div>
          </a-card>
        </a-col>

        <!-- 个性化示例 -->
        <a-col :span="12">
          <a-card title="个性化示例" size="small">
            <template #extra>
              <a-button size="small" @click="generateExample" :loading="exampleLoading">
                <template #icon><ExperimentOutlined /></template>
                生成示例
              </a-button>
            </template>

            <div class="personality-examples">
              <div v-if="personalityExample" class="example-content">
                <h6>示例对话:</h6>
                <div class="example-dialog">
                  <div class="example-input">
                    <strong>用户:</strong> {{ personalityExample.input }}
                  </div>
                  <div class="example-output">
                    <strong>{{ getCharacterName(selectedCharacter) }}:</strong> 
                    {{ personalityExample.response }}
                  </div>
                </div>

                <a-divider size="small" />

                <div class="example-analysis">
                  <h6>个性特征体现:</h6>
                  <a-space wrap>
                    <a-tag
                      v-for="feature in personalityExample.features"
                      :key="feature"
                      color="blue"
                      size="small"
                    >
                      {{ feature }}
                    </a-tag>
                  </a-space>
                </div>
              </div>

              <a-empty v-else description="点击生成示例查看个性化效果" size="small" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="mt-4" v-if="personalityData">
        <!-- 学习适应 -->
        <a-col :span="12">
          <a-card title="学习适应" size="small">
            <div class="learning-adaptation">
              <a-descriptions :column="1" size="small" bordered>
                <a-descriptions-item label="适应性学习">
                  <a-switch
                    v-model:checked="personalityData.learning_settings.adaptive_learning"
                    @change="onLearningChange"
                  />
                </a-descriptions-item>
                <a-descriptions-item label="反馈敏感度">
                  <a-slider
                    v-model:value="personalityData.learning_settings.feedback_sensitivity"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :marks="{ 0: '低', 0.5: '中', 1: '高' }"
                    @change="onLearningChange"
                  />
                </a-descriptions-item>
                <a-descriptions-item label="学习速率">
                  <a-slider
                    v-model:value="personalityData.learning_settings.learning_rate"
                    :min="0"
                    :max="1"
                    :step="0.05"
                    :marks="{ 0: '慢', 0.5: '中', 1: '快' }"
                    @change="onLearningChange"
                  />
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-card>
        </a-col>

        <!-- 个性统计 -->
        <a-col :span="12">
          <a-card title="个性统计" size="small">
            <div class="personality-stats">
              <a-row :gutter="8">
                <a-col :span="12">
                  <a-statistic
                    title="个性一致性"
                    :value="getPersonalityConsistency()"
                    :precision="1"
                    suffix="%"
                    :value-style="{ color: '#52c41a' }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="适应次数"
                    :value="personalityData.adaptation_count || 0"
                    suffix="次"
                    :value-style="{ color: '#1890ff' }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="最后更新"
                    :value="formatDate(personalityData.last_updated)"
                    :value-style="{ color: '#666' }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="版本号"
                    :value="personalityData.version || '1.0'"
                    :value-style="{ color: '#722ed1' }"
                  />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  UndoOutlined,
  SaveOutlined,
  UserOutlined,
  BgColorsOutlined,
  EditOutlined,
  InfoCircleOutlined,
  ExperimentOutlined
} from '@ant-design/icons-vue'
import { memoryApi } from '@/api/memory'

// 响应式数据
const selectedCharacter = ref('director')
const personalityLoading = ref(false)
const saveLoading = ref(false)
const exampleLoading = ref(false)

const personalityData = ref<any>(null)
const personalityExample = ref<any>(null)
const hasChanges = ref(false)

// 生命周期
onMounted(() => {
  loadPersonalityData()
})

// 方法
const loadPersonalityData = async () => {
  if (!selectedCharacter.value) return
  
  personalityLoading.value = true
  try {
    const response = await memoryApi.getPersonalityProfile(selectedCharacter.value)
    personalityData.value = response.data || getDefaultPersonality()
  } catch (error) {
    message.error('加载个性档案失败: ' + error.message)
    personalityData.value = getDefaultPersonality()
  } finally {
    personalityLoading.value = false
  }
}

const onCharacterChange = () => {
  if (hasChanges.value) {
    // 提示用户保存更改
    // 这里可以添加确认对话框
  }
  loadPersonalityData()
  personalityExample.value = null
  hasChanges.value = false
}

const onTraitChange = (trait: string, value: number) => {
  hasChanges.value = true
}

const onStyleChange = () => {
  hasChanges.value = true
}

const onBehaviorChange = () => {
  hasChanges.value = true
}

const onLearningChange = () => {
  hasChanges.value = true
}

const savePersonalityChanges = async () => {
  if (!personalityData.value) return
  
  saveLoading.value = true
  try {
    await memoryApi.adaptPersonality({
      character_id: selectedCharacter.value,
      feedback: personalityData.value
    })
    
    message.success('个性设置保存成功')
    hasChanges.value = false
    
    // 重新加载数据
    loadPersonalityData()
  } catch (error) {
    message.error('保存个性设置失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

const resetToDefaults = () => {
  personalityData.value = getDefaultPersonality()
  hasChanges.value = true
  message.info('已重置为默认设置，请保存生效')
}

const generateExample = async () => {
  exampleLoading.value = true
  try {
    const response = await memoryApi.generatePersonalizedResponse({
      character_id: selectedCharacter.value,
      input_text: '请介绍一下你的工作方式和特点',
      context: { type: 'personality_demo' }
    })
    
    personalityExample.value = {
      input: '请介绍一下你的工作方式和特点',
      response: response.data?.response || '这是一个示例响应',
      features: response.data?.personality_features || ['专业', '友好', '详细']
    }
  } catch (error) {
    message.error('生成示例失败: ' + error.message)
  } finally {
    exampleLoading.value = false
  }
}

// 辅助函数
const getDefaultPersonality = () => {
  return {
    personality_traits: {
      openness: 0.7,
      conscientiousness: 0.8,
      extraversion: 0.6,
      agreeableness: 0.7,
      neuroticism: 0.3
    },
    response_style: {
      expression_style: 'professional',
      detail_level: 0.7,
      emotional_expression: 0.5,
      creativity_bias: 0.6
    },
    behavior_preferences: {
      decision_style: 'analytical',
      communication_preferences: ['detailed', 'structured'],
      work_pace: 0.7
    },
    learning_settings: {
      adaptive_learning: true,
      feedback_sensitivity: 0.6,
      learning_rate: 0.5
    },
    adaptation_count: 0,
    last_updated: new Date().toISOString(),
    version: '1.0'
  }
}

const getTraitName = (trait: string) => {
  const names = {
    openness: '开放性',
    conscientiousness: '尽责性',
    extraversion: '外向性',
    agreeableness: '宜人性',
    neuroticism: '神经质'
  }
  return names[trait] || trait
}

const getTraitDescription = (trait: string, value: number) => {
  const descriptions = {
    openness: value > 0.6 ? '富有想象力，喜欢新体验' : '偏向传统，注重实用性',
    conscientiousness: value > 0.6 ? '有组织性，注重细节' : '灵活随性，适应性强',
    extraversion: value > 0.6 ? '外向活跃，善于交际' : '内向沉稳，深思熟虑',
    agreeableness: value > 0.6 ? '友善合作，善解人意' : '直接坦率，独立自主',
    neuroticism: value > 0.6 ? '情绪敏感，容易焦虑' : '情绪稳定，抗压能力强'
  }
  return descriptions[trait] || ''
}

const getCharacterName = (characterId: string) => {
  const names = {
    director: '导演',
    designer: '设计师',
    writer: '撰写师'
  }
  return names[characterId] || characterId
}

const getPersonalityConsistency = () => {
  if (!personalityData.value) return 0
  
  // 简单的一致性计算
  const traits = Object.values(personalityData.value.personality_traits) as number[]
  const variance = traits.reduce((sum, val, _, arr) => {
    const mean = arr.reduce((s, v) => s + v, 0) / arr.length
    return sum + Math.pow(val - mean, 2)
  }, 0) / traits.length
  
  return Math.max(0, 100 - variance * 100)
}

const formatDate = (dateString: string) => {
  if (!dateString) return '--'
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.personality-settings {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.settings-content {
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.personality-traits {
  padding: 8px 0;
}

.trait-item {
  margin-bottom: 24px;
}

.trait-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.trait-name {
  font-weight: 500;
  color: #262626;
}

.trait-value {
  font-weight: bold;
  color: #1890ff;
}

.trait-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.response-styles {
  padding: 8px 0;
}

.behavior-preferences {
  padding: 8px 0;
}

.personality-examples {
  padding: 8px 0;
}

.example-content h6 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
}

.example-dialog {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.example-input,
.example-output {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 13px;
}

.example-input {
  color: #666;
}

.example-output {
  color: #262626;
}

.example-analysis h6 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 13px;
}

.learning-adaptation {
  padding: 8px 0;
}

.personality-stats {
  padding: 8px 0;
}

:deep(.ant-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-slider) {
  margin: 8px 0 16px 0;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  width: 100px;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-size: 13px;
}
</style>
