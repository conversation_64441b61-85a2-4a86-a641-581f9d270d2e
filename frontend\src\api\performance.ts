/**
 * 性能监控相关API
 */

import { request } from './request'

export interface HealthCheckResponse {
  health_status: 'healthy' | 'warning' | 'critical'
  timestamp: string
  issues: string[]
  uptime: number
  version: string
}

export interface MetricsResponse {
  response_time: number
  memory_usage: number
  cpu_usage: number
  cache_hit_rate: number
  concurrent_requests: number
  error_rate: number
  timestamp: string
}

export interface SystemInfoResponse {
  platform: string
  python_version: string
  cpu_count: number
  memory: {
    total: number
    available: number
    used: number
    percent: number
  }
  disk: {
    total: number
    used: number
    free: number
    percent: number
  }
  network: {
    bytes_sent: number
    bytes_recv: number
    packets_sent: number
    packets_recv: number
  }
}

export interface PerformanceSummaryResponse {
  current_metrics: {
    response_time: number
    memory_usage: number
    cpu_usage: number
    cache_hit_rate: number
  }
  averages: {
    avg_response_time: number
    avg_memory_usage: number
    avg_cpu_usage: number
    avg_cache_hit_rate: number
  }
  cache_stats: {
    total_entries: number
    hits: number
    misses: number
    hit_rate: number
  }
  optimization_score: number
  last_optimization: string
}

export interface RecommendationsResponse {
  recommendations: string[]
  priority_level: 'low' | 'medium' | 'high' | 'critical'
  estimated_improvement: number
  timestamp: string
}

export interface BenchmarkResult {
  test_name: string
  duration: number
  operations_per_second: number
  memory_usage: number
  success_rate: number
  timestamp: string
}

export interface OptimizationRequest {
  optimization_type: 'memory' | 'cache' | 'performance' | 'all'
  force: boolean
}

export const performanceApi = {
  // 健康检查
  async getHealthCheck() {
    return request.get<HealthCheckResponse>('/api/performance/health')
  },

  // 性能指标
  async getMetrics() {
    return request.get<MetricsResponse>('/api/performance/metrics')
  },

  // 系统信息
  async getSystemInfo() {
    return request.get<SystemInfoResponse>('/api/performance/system-info')
  },

  // 性能摘要
  async getSummary() {
    return request.get<PerformanceSummaryResponse>('/api/performance/summary')
  },

  // 优化建议
  async getRecommendations() {
    return request.get<RecommendationsResponse>('/api/performance/recommendations')
  },

  // 性能基准测试
  async runBenchmark(testType: string = 'all') {
    return request.post<BenchmarkResult[]>('/api/performance/benchmark', { test_type: testType })
  },

  // 获取基准测试历史
  async getBenchmarkHistory(limit: number = 10) {
    return request.get<BenchmarkResult[]>(`/api/performance/benchmark/history?limit=${limit}`)
  },

  // 执行优化
  async optimize(data: OptimizationRequest) {
    return request.post('/api/performance/optimize', data)
  },

  // 获取优化历史
  async getOptimizationHistory(limit: number = 10) {
    return request.get(`/api/performance/optimization/history?limit=${limit}`)
  },

  // 缓存管理
  async getCacheStats() {
    return request.get('/api/performance/cache/stats')
  },

  async clearCache(cacheType: string = 'all') {
    return request.post('/api/performance/cache/clear', { cache_type: cacheType })
  },

  async warmupCache() {
    return request.post('/api/performance/cache/warmup')
  },

  // 内存管理
  async getMemoryStats() {
    return request.get('/api/performance/memory/stats')
  },

  async forceGarbageCollection() {
    return request.post('/api/performance/memory/gc')
  },

  // 监控配置
  async getMonitoringConfig() {
    return request.get('/api/performance/config')
  },

  async updateMonitoringConfig(config: any) {
    return request.put('/api/performance/config', config)
  },

  // 告警管理
  async getAlerts() {
    return request.get('/api/performance/alerts')
  },

  async acknowledgeAlert(alertId: string) {
    return request.post(`/api/performance/alerts/${alertId}/acknowledge`)
  },

  async dismissAlert(alertId: string) {
    return request.delete(`/api/performance/alerts/${alertId}`)
  },

  // 性能报告
  async generateReport(timeRange: string = '24h') {
    return request.post('/api/performance/report', { time_range: timeRange })
  },

  async getReports(limit: number = 10) {
    return request.get(`/api/performance/reports?limit=${limit}`)
  },

  async downloadReport(reportId: string) {
    return request.get(`/api/performance/reports/${reportId}/download`, {
      responseType: 'blob'
    })
  },

  // 实时监控
  async startRealTimeMonitoring() {
    return request.post('/api/performance/monitoring/start')
  },

  async stopRealTimeMonitoring() {
    return request.post('/api/performance/monitoring/stop')
  },

  async getMonitoringStatus() {
    return request.get('/api/performance/monitoring/status')
  },

  // 性能阈值管理
  async getThresholds() {
    return request.get('/api/performance/thresholds')
  },

  async updateThresholds(thresholds: any) {
    return request.put('/api/performance/thresholds', thresholds)
  },

  async resetThresholds() {
    return request.post('/api/performance/thresholds/reset')
  },

  // 性能趋势分析
  async getTrends(timeRange: string = '7d', metric: string = 'all') {
    return request.get(`/api/performance/trends?time_range=${timeRange}&metric=${metric}`)
  },

  async getPredictions(metric: string, horizon: string = '1h') {
    return request.get(`/api/performance/predictions?metric=${metric}&horizon=${horizon}`)
  },

  // 负载测试
  async startLoadTest(config: any) {
    return request.post('/api/performance/load-test/start', config)
  },

  async stopLoadTest(testId: string) {
    return request.post(`/api/performance/load-test/${testId}/stop`)
  },

  async getLoadTestResults(testId: string) {
    return request.get(`/api/performance/load-test/${testId}/results`)
  },

  async getLoadTestHistory(limit: number = 10) {
    return request.get(`/api/performance/load-test/history?limit=${limit}`)
  }
}

export default performanceApi
