<template>
  <div class="agent-interaction-test">
    <a-page-header
      title="AI角色交互测试"
      sub-title="测试@导演、@设计师、@撰写师功能"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-tag :color="getConnectionColor()">
            Socket状态: {{ connectionStatus }}
          </a-tag>
          <a-button @click="reconnectSocket" :loading="connecting">
            <template #icon><ReloadOutlined /></template>
            重连
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <div class="test-content">
      <a-row :gutter="16">
        <!-- 左侧：测试控制面板 -->
        <a-col :span="8">
          <a-card title="测试控制面板" size="small">
            <a-space direction="vertical" style="width: 100%">
              <a-form layout="vertical">
                <a-form-item label="选择测试角色">
                  <a-select v-model:value="selectedAgent" placeholder="选择要测试的AI角色">
                    <a-select-option value="导演">🎬 导演</a-select-option>
                    <a-select-option value="设计师">🎨 设计师</a-select-option>
                    <a-select-option value="撰写师">✍️ 撰写师</a-select-option>
                  </a-select>
                </a-form-item>

                <a-form-item label="测试消息">
                  <a-textarea
                    v-model:value="testMessage"
                    placeholder="输入要发送给AI角色的消息..."
                    :rows="3"
                  />
                </a-form-item>

                <a-form-item>
                  <a-space>
                    <a-button 
                      type="primary" 
                      @click="sendTestMessage"
                      :disabled="!selectedAgent || !testMessage.trim()"
                      :loading="sendingMessage"
                    >
                      <template #icon><SendOutlined /></template>
                      发送@{{ selectedAgent }}
                    </a-button>
                    <a-button @click="clearMessages">
                      <template #icon><ClearOutlined /></template>
                      清空
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-form>

              <a-divider>快速测试</a-divider>
              
              <a-space direction="vertical" style="width: 100%">
                <a-button block @click="quickTest('导演', '请帮我规划一个科幻故事的剧情')">
                  测试导演：剧情规划
                </a-button>
                <a-button block @click="quickTest('设计师', '请设计一个未来战士角色')">
                  测试设计师：角色设计
                </a-button>
                <a-button block @click="quickTest('撰写师', '写一段关于星际旅行的描述')">
                  测试撰写师：文本创作
                </a-button>
                <a-button block @click="testAllAgents">
                  测试所有角色协作
                </a-button>
              </a-space>
            </a-space>
          </a-card>
        </a-col>

        <!-- 右侧：聊天消息区域 -->
        <a-col :span="16">
          <a-card title="聊天消息" size="small">
            <template #extra>
              <a-space>
                <a-tag color="blue">消息数: {{ messages.length }}</a-tag>
                <a-tag v-if="isLoading" color="orange">
                  <template #icon><LoadingOutlined spin /></template>
                  AI思考中...
                </a-tag>
              </a-space>
            </template>

            <div class="message-container" ref="messageContainer">
              <div 
                v-for="message in messages" 
                :key="message.id"
                class="message-item"
                :class="message.type"
              >
                <div class="message-header">
                  <a-avatar 
                    :style="{ backgroundColor: getAvatarColor(message.type) }"
                    size="small"
                  >
                    {{ getAvatarText(message.sender) }}
                  </a-avatar>
                  <span class="sender-name">{{ message.sender }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  {{ message.content }}
                </div>
                <div v-if="message.processing_time" class="message-meta">
                  <a-tag size="small" color="green">
                    响应时间: {{ message.processing_time.toFixed(2) }}s
                  </a-tag>
                </div>
              </div>

              <div v-if="messages.length === 0" class="empty-messages">
                <a-empty description="暂无消息，开始测试AI角色交互吧！" />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 测试结果统计 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="测试统计" size="small">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="总消息数"
                  :value="messages.length"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="AI响应数"
                  :value="agentMessages.length"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="平均响应时间"
                  :value="averageResponseTime"
                  suffix="s"
                  :precision="2"
                  :value-style="{ color: '#fa8c16' }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="成功率"
                  :value="successRate"
                  suffix="%"
                  :precision="1"
                  :value-style="{ color: successRate > 80 ? '#52c41a' : '#f5222d' }"
                />
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  SendOutlined,
  ClearOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue'
import { useChatStore } from '@/stores/chatStore'
import { useSocketStore } from '@/stores/socketStore'

// 响应式数据
const selectedAgent = ref('')
const testMessage = ref('')
const sendingMessage = ref(false)
const connecting = ref(false)
const messageContainer = ref<HTMLElement>()

// Store
const chatStore = useChatStore()
const socketStore = useSocketStore()

// 计算属性
const messages = computed(() => chatStore.messages)
const isLoading = computed(() => chatStore.isLoading)
const connectionStatus = computed(() => socketStore.connectionStatus)

const agentMessages = computed(() => 
  messages.value.filter(msg => msg.type === 'agent')
)

const averageResponseTime = computed(() => {
  const responseTimes = agentMessages.value
    .map(msg => msg.processing_time)
    .filter(time => time && time > 0)
  
  if (responseTimes.length === 0) return 0
  return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
})

const successRate = computed(() => {
  const totalTests = messages.value.filter(msg => msg.type === 'user').length
  const successfulResponses = agentMessages.value.length
  
  if (totalTests === 0) return 0
  return (successfulResponses / totalTests) * 100
})

// 方法
const getConnectionColor = () => {
  switch (connectionStatus.value) {
    case 'connected': return 'green'
    case 'connecting': return 'orange'
    case 'error': return 'red'
    default: return 'default'
  }
}

const getAvatarColor = (type: string) => {
  const colorMap: Record<string, string> = {
    user: '#1890ff',
    agent: '#52c41a',
    system: '#fa8c16'
  }
  return colorMap[type] || '#d9d9d9'
}

const getAvatarText = (sender: string) => {
  if (sender === '用户') return '我'
  if (sender === '系统') return '系'
  return sender.charAt(0)
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const sendTestMessage = async () => {
  if (!selectedAgent.value || !testMessage.value.trim()) return
  
  sendingMessage.value = true
  
  try {
    const fullMessage = `@${selectedAgent.value} ${testMessage.value}`
    await chatStore.sendMessage(fullMessage)
    
    testMessage.value = ''
    message.success(`已发送消息给${selectedAgent.value}`)
    
    // 滚动到底部
    await scrollToBottom()
    
  } catch (error) {
    message.error('发送消息失败: ' + error.message)
  } finally {
    sendingMessage.value = false
  }
}

const quickTest = async (agent: string, msg: string) => {
  selectedAgent.value = agent
  testMessage.value = msg
  await sendTestMessage()
}

const testAllAgents = async () => {
  const tests = [
    { agent: '导演', message: '请为我们的项目制定一个创作计划' },
    { agent: '设计师', message: '请设计主角的外观和性格特征' },
    { agent: '撰写师', message: '请写一个引人入胜的开头段落' }
  ]
  
  for (const test of tests) {
    await quickTest(test.agent, test.message)
    await new Promise(resolve => setTimeout(resolve, 2000)) // 等待2秒
  }
}

const clearMessages = () => {
  chatStore.clearMessages()
  message.success('消息已清空')
}

const reconnectSocket = async () => {
  connecting.value = true
  try {
    socketStore.disconnect()
    await new Promise(resolve => setTimeout(resolve, 1000))
    socketStore.connect()
    message.success('Socket重连成功')
  } catch (error) {
    message.error('Socket重连失败')
  } finally {
    connecting.value = false
  }
}

const scrollToBottom = async () => {
  await nextTick()
  if (messageContainer.value) {
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight
  }
}

// 生命周期
onMounted(() => {
  // 确保Socket连接
  if (socketStore.connectionStatus !== 'connected') {
    socketStore.connect()
  }
})
</script>

<style scoped>
.agent-interaction-test {
  padding: 16px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.message-container {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.message-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-item.user {
  margin-left: 20%;
  background: #e6f7ff;
}

.message-item.agent {
  margin-right: 20%;
  background: #f6ffed;
}

.message-item.system {
  background: #fff7e6;
  text-align: center;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.sender-name {
  font-weight: 500;
  color: #262626;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.message-content {
  color: #262626;
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-meta {
  margin-top: 8px;
  text-align: right;
}

.empty-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
